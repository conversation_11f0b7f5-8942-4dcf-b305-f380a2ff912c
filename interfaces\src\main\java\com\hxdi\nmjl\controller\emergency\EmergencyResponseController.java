package com.hxdi.nmjl.controller.emergency;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.domain.emergency.EmergencyResponse;
import com.hxdi.nmjl.framework.anno.SysLog;
import com.hxdi.nmjl.condition.emergency.EmergencyResponseCondition;
import com.hxdi.nmjl.service.emergency.EmergencyResponseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <应急预案管理接口>
 * <AUTHOR>
 * @version 1.0
 * @since 2025/6/25
 */
@Api(tags = "应急预案管理")
@RestController
@RequestMapping("/emergencyResponse")
public class EmergencyResponseController extends BaseController<EmergencyResponseService, EmergencyResponse> {

    @ApiOperation("分页查询")
    @GetMapping("/page")
    public ResultBody<Page<EmergencyResponse>> getPages(EmergencyResponseCondition condition) {
        return ResultBody.ok().data(bizService.getPages(condition));
    }

    @ApiOperation("列表查询")
    @GetMapping("/list")
    public ResultBody<List<EmergencyResponse>> getList(EmergencyResponseCondition condition) {
        return ResultBody.ok().data(bizService.getList(condition));
    }

    @ApiOperation("查看详情")
    @GetMapping("/getDetail")
    public ResultBody<EmergencyResponse> getDetail(@RequestParam String id) {
        return ResultBody.ok().data(bizService.getDetail(id));
    }

    @ApiOperation("删除应急预案信息")
    @PostMapping("/delete")
    public ResultBody<Boolean> delete(@RequestParam String id) {
        return ResultBody.ok().data(bizService.delete(id));
    }

    @ApiOperation(value = "添加/修改")
    @PostMapping("/saveOrUpdate")
    @SysLog
    public ResultBody saveOrUpdate(@RequestBody EmergencyResponse emergencyResponse) {
        if (CommonUtils.isEmpty(emergencyResponse.getId())) {
            bizService.add(emergencyResponse);
        } else {
            bizService.update(emergencyResponse);
        }
        return ResultBody.ok();
    }
    @ApiOperation(value = "审核")
    @GetMapping("/approve")
    public ResultBody<Void> approve(@RequestParam String id, @RequestParam String approveOpinion) {
        bizService.approve(id, approveOpinion);
        return ResultBody.ok();
    }

    @ApiOperation(value = "驳回")
    @GetMapping("/reject")
    public ResultBody<Void> reject(@RequestParam String id, @RequestParam String approveOpinion) {
        bizService.reject(id, approveOpinion);
        return ResultBody.ok();
    }

    @ApiOperation(value = "提交")
    @GetMapping("/submit")
    public ResultBody<Void> submit(@RequestParam String id) {
        bizService.submit(id);
        return ResultBody.ok();
    }
}
