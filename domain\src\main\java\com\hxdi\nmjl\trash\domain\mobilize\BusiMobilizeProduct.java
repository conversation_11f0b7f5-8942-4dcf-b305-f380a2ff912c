package com.hxdi.nmjl.trash.domain.mobilize;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 动员商品/服务
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("BUSI_MOBILIZE_PRODUCT")
public class BusiMobilizeProduct extends DataEntity<BusiMobilizeProduct> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("ENTERPRISE_ID")
    private Integer enterpriseId;

    @TableField("FIRST_CATEGORY_ID")
    private Integer firstCategoryId;

    @TableField("SECOND_CATEGORY_ID")
    private Integer secondCategoryId;

    @TableField("CODE")
    private String code;

    @TableField("SELF_CODE")
    private String selfCode;

    @TableField("REMEMBER_CODE")
    private String rememberCode;

    @TableField("NAME")
    private String name;

    @TableField("SPEC")
    private String spec;

    @TableField("SHORT_NAME")
    private String shortName;

    @TableField("UNIT")
    private String unit;

    @TableField("ORIGIN")
    private String origin;

    @TableField("TYPE")
    private String type;

    @TableField("CONTACT")
    private String contact;

    @TableField("CONTACT_BY")
    private String contactBy;

    @TableField("QUALITY_LEVEL")
    private String qualityLevel;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;
}
