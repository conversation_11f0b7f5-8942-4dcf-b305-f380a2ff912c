package com.hxdi.nmjl.trash.domain.supply;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 订单详情
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("BUSI_SUPPLY_ORDER_INFO")
public class BusiSupplyOrderInfo extends DataEntity<BusiSupplyOrderInfo> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("ORDER_ID")
    private Integer orderId;

    @TableField("GOODS_ID")
    private Integer goodsId;

    @TableField("CATEGORY")
    private String category;

    @TableField("VARIETY")
    private String variety;

    @TableField("BRAND")
    private String brand;

    @TableField("NAME")
    private String name;

    @TableField("SPEC")
    private String spec;

    @TableField("UNIT")
    private String unit;

    @TableField("QUANTITY")
    private Integer quantity;

    @TableField("AMOUNT")
    private BigDecimal amount;

    @TableField("QUALITY_LEVEL")
    private String qualityLevel;

    @TableField("DESC_TITLE")
    private String descTitle;

    @TableField("DESC_CONTENT")
    private String descContent;

    @TableField("AFTER_SALES_DESC")
    private String afterSalesDesc;

    @TableField("RECEIVE_TYPE")
    private String receiveType;

    @TableField("SELF_TAKE_ADDRESS")
    private String selfTakeAddress;

    @TableField("SELF_TAKE_CODE")
    private String selfTakeCode;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;
}
