package com.hxdi.nmjl.service.bigscreen.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.base.OrganizationCondition;
import com.hxdi.nmjl.condition.bigscreen.OrderMonthRecordCondition;
import com.hxdi.nmjl.condition.inventory.InventoryCondition;
import com.hxdi.nmjl.condition.mobilization.MobilizedEnterpriseCondition;
import com.hxdi.nmjl.condition.msg.WarningInfoCondition;
import com.hxdi.nmjl.condition.plan.ProcurementPlanCondition;
import com.hxdi.nmjl.domain.base.Organization;
import com.hxdi.nmjl.domain.bigscreen.BigScreenConfig;
import com.hxdi.nmjl.domain.bigscreen.OrgPoint;
import com.hxdi.nmjl.domain.common.WarningInfo;
import com.hxdi.nmjl.domain.inventory.Inventory;
import com.hxdi.nmjl.domain.mobilization.MobilizedEnterprise;
import com.hxdi.nmjl.dto.base.StoreCapacityDTO;
import com.hxdi.nmjl.service.base.OrganizationService;
import com.hxdi.nmjl.service.base.StoreHouseService;
import com.hxdi.nmjl.service.bigscreen.BigScreenConfigService;
import com.hxdi.nmjl.service.bigscreen.BigScreenService;
import com.hxdi.nmjl.service.bigscreen.InfoAreaService;
import com.hxdi.nmjl.service.bigscreen.OrderMonthRecordService;
import com.hxdi.nmjl.service.common.WarningInfoService;
import com.hxdi.nmjl.service.inventory.InventoryService;
import com.hxdi.nmjl.service.mobilization.MobilizedEnterpriseService;
import com.hxdi.nmjl.service.plan.ProcurementPlanService;
import com.hxdi.nmjl.vo.bigscreen.AreaOrderStatVO;
import com.hxdi.nmjl.vo.bigscreen.InventorySumAndCapacityVO;
import com.hxdi.nmjl.vo.bigscreen.ProcurementPlanSummaryVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: nmjl-service
 * @description: 大屏服务实现类
 * @author: 王贝强
 * @create: 2025-07-28 15:21
 */
@Service
public class BigScreenServiceImpl implements BigScreenService {

    @Resource
    private InfoAreaService infoAreaService;

    @Resource
    private InventoryService inventoryService;

    @Resource
    private BigScreenConfigService bigScreenConfigService;

    @Resource
    private OrganizationService organizationService;

    @Resource
    private StoreHouseService storeHouseService;

    @Resource
    private OrderMonthRecordService orderMonthRecordService;

    @Resource
    private MobilizedEnterpriseService mobilizedEnterpriseService;

    @Resource
    private WarningInfoService warningInfoService;

    @Resource
    private ProcurementPlanService procurementPlanService;

    @Override
    public List<BigScreenConfig> getScreenConfig(String ConfigKey) {
        if (CommonUtils.isEmpty(ConfigKey)) {
            return Collections.emptyList();
        }
        return bigScreenConfigService.getConfigListByKey(ConfigKey);
    }

    @Override
    public List<AreaOrderStatVO> getAreaMonthlyOrderSummary(OrderMonthRecordCondition condition) {
        return orderMonthRecordService.getAreaMonthlyOrderSummary(condition);
    }

    @Override
    public List<AreaOrderStatVO> getAreaMonthlyCategoryOrderSummary(OrderMonthRecordCondition condition) {
        //获取品种分类配置
        List<BigScreenConfig> list = bigScreenConfigService.buildCategoryClassification();
        return orderMonthRecordService.getAreaMonthlyCategoryOrderSummary(condition, list);
    }

    @Override
    public Map<Integer, Integer> getOrgNum(String areaCode) {
        OrganizationCondition condition = new OrganizationCondition();
        if (areaCode == null || areaCode.isEmpty()) {
            areaCode = "150000";
        }
        //查询该地区及子地区的所有地区代码
        List<String> areaCodes = infoAreaService.getAllChildAreaCodes(areaCode);
        areaCodes.add(areaCode);
        condition.setAreaCode(String.join(",", areaCodes));
        //先获取机构数量
        Map<Integer, Integer> orgNum = organizationService.getOrgNum(condition);
        if (orgNum.isEmpty()) {
            orgNum = new HashMap<>();
        }
        //再单独获取动员企业的数量
        MobilizedEnterpriseCondition enterpriseCondition = new MobilizedEnterpriseCondition();
        enterpriseCondition.setAreaCode(String.join(",", areaCodes));
        List<MobilizedEnterprise> enterpriseNum = mobilizedEnterpriseService.lists(enterpriseCondition);
        orgNum.put(-1, enterpriseNum.size());
        return orgNum;
    }

    @Override
    public Page<WarningInfo> getWarningInfoPage(WarningInfoCondition condition) {
        return warningInfoService.getBigScreenPageByCondition(condition);
    }

    @Override
    public Map<String, BigDecimal> getInventorySummary(String areaCode) {
        if (CommonUtils.isEmpty(areaCode)) {
            areaCode = "150000";
        }
        // 查询该地区下属的所有层级地区编码
        List<String> allChildAreaCodes = infoAreaService.getAllChildAreaCodes(areaCode);
        List<BigScreenConfig> configList = bigScreenConfigService.buildCategoryClassification();
        return inventoryService.getBigScreenSummary(allChildAreaCodes, configList);
    }

    @Override
    public Page<Inventory> getInventorySummaryPage(InventoryCondition condition) {
        return inventoryService.getBigScreenSummaryPage(condition);
    }

    @Override
    public List<InventorySumAndCapacityVO> getInventorySumAndCapacity(String areaCode) {
        return inventoryService.getInventorySumAndCapacity(areaCode);
    }

    @Override
    public Page<InventorySumAndCapacityVO> getInventorySumAndCapacityPage(String areaCode, String storeId) {
        return inventoryService.getInventorySumAndCapacityPage(areaCode, storeId);
    }

    @Override
    public ProcurementPlanSummaryVO getPlanSummary(String areaCode) {
        return procurementPlanService.getPlanSummary(areaCode);
    }

    @Override
    public Page<ProcurementPlanSummaryVO> getPlanSummaryPage(ProcurementPlanCondition condition) {
        return procurementPlanService.getPlanSummaryPage(condition);
    }

    @Override
    public List<OrgPoint> getOrgPoint(String areaCode) {
        List<OrgPoint> orgPointList = new ArrayList<>();

        OrganizationCondition condition = new OrganizationCondition();
        if (areaCode == null || areaCode.isEmpty()) {
            areaCode = "150000";
        }
        //查询该地区及子地区的所有地区代码
        List<String> areaCodes = infoAreaService.getAllChildAreaCodes(areaCode);
        areaCodes.add(areaCode);
        condition.setOrgType(2);
        condition.setAreaCode(String.join(",", areaCodes));
        //查询对应的军供站的坐标
        List<Organization> organizationList = organizationService.lists(condition);
        Map<String, String> capacities;
        //查询对应的军供站的库容
        if (organizationList != null && !organizationList.isEmpty()) {
            List<String> storeIds = organizationList.stream().map(Organization::getId).collect(Collectors.toList());
            capacities = storeHouseService.getCapacityByStoreId(storeIds).stream().collect(Collectors.toMap(StoreCapacityDTO::getStoreId, StoreCapacityDTO::getCapacity));
        } else {
            capacities = Collections.emptyMap();
        }
        //再单独获取动员企业的数量
        MobilizedEnterpriseCondition enterpriseCondition = new MobilizedEnterpriseCondition();
        enterpriseCondition.setAreaCode(String.join(",", areaCodes));
        List<MobilizedEnterprise> enterpriseList = mobilizedEnterpriseService.lists(enterpriseCondition);
        //构建每个机构的名称、类型、经纬度坐标并返回

        //构建机构坐标点
        if (organizationList != null && !organizationList.isEmpty()) {
            organizationList.forEach(org -> {
                OrgPoint point = new OrgPoint();
                point.setOrgName(org.getOrgName());
                point.setOrgFunctional(org.getFunctional() != null ? String.valueOf(org.getFunctional()) : "0");
                point.setFzr(org.getFzr());
                point.setMobile(org.getMobile());
                point.setCapacity(capacities.get(org.getId()));
                point.setLon(org.getLon());
                point.setLat(org.getLat());
                orgPointList.add(point);
            });
        }

        //构建企业坐标点
        if (enterpriseList != null && !enterpriseList.isEmpty()) {
            enterpriseList.forEach(enterprise -> {
                OrgPoint point = new OrgPoint();
                point.setOrgName(enterprise.getEnterpriseName());
                point.setOrgFunctional("-1");
                point.setOrgType(enterprise.getType());
                point.setFzr(enterprise.getPrincipal());
                point.setMobile(enterprise.getPrincipalTel());
                point.setLon(enterprise.getLon());
                point.setLat(enterprise.getLat());
                orgPointList.add(point);
            });
        }
        return orgPointList;
    }
}
