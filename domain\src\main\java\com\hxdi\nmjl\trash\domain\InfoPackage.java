package com.hxdi.nmjl.trash.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 包装
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("INFO_PACKAGE")
public class InfoPackage extends DataEntity<InfoPackage> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("PID")
    private Integer pid;

    @TableField("NAME")
    private String name;

    @TableField("NICK_NAME")
    private String nickName;

    @TableField("CODER")
    private String coder;

    @TableField("CAPACITY")
    private String capacity;

    @TableField("CAPACITY_COUNT")
    private String capacityCount;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;

}
