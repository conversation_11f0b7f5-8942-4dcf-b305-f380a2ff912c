package com.hxdi.nmjl.condition.inventory;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel(description = "库容查询条件")
public class StorageCapacityCondition extends QueryCondition {

    @ApiModelProperty(value = "管理单位名称")
    private String orgName;

    @ApiModelProperty(value = "仓房名称")
    private String stName;

    @ApiModelProperty(value = "军供站名称")
    private String storeName;

    @ApiModelProperty(value = "业务状态：1-闲置，2-租用，3-政策性占用")
    private Integer state;

    private Integer approveStatus;
}
