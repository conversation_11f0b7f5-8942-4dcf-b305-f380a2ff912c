package com.hxdi.nmjl.trash.domain.mobilize;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * @ClassName: BusiMobilizeCategory
 * @Author: *
 * @Description:动员商品 类别
 * @Date: Thu Jul 01 09:58:01 CST 2021
 */

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@TableName("BUSI_MOBILIZE_CATEGORY")
public class BusiMobilizeCategory extends DataEntity<BusiMobilizeCategory> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("PID")
    private Integer pid;

    @TableField("NAME")
    private String name;

    @TableField("UNIT")
    private String unit;

    @TableField("SPEC")
    private String spec;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;
}
