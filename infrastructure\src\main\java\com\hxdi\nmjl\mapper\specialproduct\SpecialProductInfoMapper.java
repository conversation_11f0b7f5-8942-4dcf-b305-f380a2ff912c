package com.hxdi.nmjl.mapper.specialproduct;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.specialproduct.SpecialProductInfo;
import com.hxdi.nmjl.vo.condition.specialproduct.SpecialProductInfoCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 地方特色产品信息Mapper接口
 */
public interface SpecialProductInfoMapper extends SuperMapper<SpecialProductInfo> {

    List<SpecialProductInfo> selectListV1(@Param("condition") SpecialProductInfoCondition condition);

    Page<SpecialProductInfo> selectPageV1(Page<SpecialProductInfo> page, @Param("condition") SpecialProductInfoCondition condition);
}