package com.hxdi.nmjl.trash.domain.product;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 品种申请
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("BUSI_TMP_VARIETY_APPLY")
public class BusiTmpVarietyApply extends DataEntity<BusiTmpVarietyApply> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("CODE")
    private String code;

    @TableField("USCC")
    private String uscc;

    @TableField("LEGAL_PERSON_CERTIFICATE")
    private String legalPersonCertificate;

    @TableField("AREA_CODE")
    private String areaCode;

    @TableField("VARIETY_NAME")
    private String varietyName;

    @TableField("BRAND_ID")
    private Integer brandId;

    @TableField("SPEC")
    private String spec;

    @TableField("APPLY_PERSON")
    private String applyPerson;

    @TableField("APPLY_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date applyTime;

    @TableField("APPLY_STATUS")
    private String applyStatus;

    @TableField("BRAND_CODE")
    private String brandCode;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;
}
