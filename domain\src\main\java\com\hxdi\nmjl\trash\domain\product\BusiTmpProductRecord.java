package com.hxdi.nmjl.trash.domain.product;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 产品备案
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("BUSI_TMP_PRODUCT_RECORD")
public class BusiTmpProductRecord extends DataEntity<BusiTmpProductRecord> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("CODE")
    private String code;

    @TableField("PRODUCT_NAME")
    private String productName;

    @TableField("SPEC")
    private String spec;

    @TableField("UNIT")
    private String unit;

    @TableField("QUALITY_LEVEL")
    private String qualityLevel;

    @TableField("SHELF_LIFE")
    private String shelfLife;

    @TableField("ORIGIN")
    private String origin;

    @TableField("PROD_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date prodTime;

    @TableField("CONTACT")
    private String contact;

    @TableField("CONTACT_PHONE")
    private String contactPhone;

    @TableField("FAX")
    private String fax;

    @TableField("EMAIL")
    private String email;

    @TableField("QQ")
    private String qq;

    @TableField("ENTERPRISE_PROFILE")
    private String enterpriseProfile;

    @TableField("PROD_INTRODUCTION")
    private String prodIntroduction;

    @TableField("ENTERPRISE_APPROVE_OPINION")
    private String enterpriseApproveOpinion;

    @TableField("ENTERPRISE_APPROVE_PERSON")
    private String enterpriseApprovePerson;

    @TableField("ENTERPRISE_APPROVE_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date enterpriseApproveTime;

    @TableField("CITY_APPROVE_OPINION")
    private String cityApproveOpinion;

    @TableField("CITY_APPROVE_PERSON")
    private String cityApprovePerson;

    @TableField("CITY_APPROVE_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date cityApproveTime;

    @TableField("PROVINCE_APPROVE_OPINION")
    private String provinceApproveOpinion;

    @TableField("PROVINCE_APPROVE_PERSON")
    private String provinceApprovePerson;

    @TableField("PROVINCE_APPROVE_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date provinceApproveTime;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;
}
