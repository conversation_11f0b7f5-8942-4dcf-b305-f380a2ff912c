package com.hxdi.nmjl.service.base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.base.ClientSaleConfig;
import com.hxdi.nmjl.condition.base.ClientSaleConfigCondition;

import java.util.List;

/**
 * 客户销售配置服务接口
 */
public interface ClientSaleConfigService extends IBaseService<ClientSaleConfig> {

    /**
     * 查询客户销售配置详情
     *
     * @param id 配置ID
     * @return ClientSaleConfig
     */
    ClientSaleConfig getDetail(String id);

    /**
     * 分页查询客户销售配置信息
     *
     * @param condition 查询条件
     * @return Page<ClientSaleConfig>
     */
    Page<ClientSaleConfig> getPages(ClientSaleConfigCondition condition);

    /**
     * 列表查询客户销售配置信息
     *
     * @param condition 查询条件
     * @return List<ClientSaleConfig>
     */
    List<ClientSaleConfig> getList(ClientSaleConfigCondition condition);

    /**
     * 新增客户销售配置信息
     *
     * @param clientSaleConfig 客户销售配置信息
     */
    void add(ClientSaleConfig clientSaleConfig);

    /**
     * 更新客户销售配置信息
     *
     * @param clientSaleConfig 客户销售配置信息
     */
    void update(ClientSaleConfig clientSaleConfig);

    /**
     * 删除客户销售配置信息
     *
     * @param id 配置ID
     * @return boolean
     */
    boolean delete(String id);
}
