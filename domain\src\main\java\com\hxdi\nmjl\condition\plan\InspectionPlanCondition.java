package com.hxdi.nmjl.condition.plan;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@ApiModel(description = "监察计划查询参数")
@Setter
@Getter
public class InspectionPlanCondition extends QueryCondition {
    @ApiModelProperty(value = "开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "业务状态：0-未执行，1-执行中，2-已立案，3-已结案", example = "0")
    private Integer state;

    @ApiModelProperty(value = "计划编号")
    private String planCode;
}
