package com.hxdi.nmjl.service.base.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.security.BaseUserDetails;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.nmjl.domain.base.ProductionSupplier;
import com.hxdi.nmjl.domain.base.SupplierEvaluation;
import com.hxdi.nmjl.enums.SupplierEvaluationDimension;
import com.hxdi.nmjl.mapper.base.SupplierEvaluationMapper;
import com.hxdi.nmjl.service.base.SupplierEvaluationService;
import com.hxdi.nmjl.service.base.SupplierService;
import com.hxdi.nmjl.vo.condition.base.SupplierEvaluationCondition;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

/**
 * 供应商评价服务实现类
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SupplierEvaluationServiceImpl extends BaseServiceImpl<SupplierEvaluationMapper, SupplierEvaluation> implements SupplierEvaluationService {

    @Autowired
    private SupplierEvaluationMapper supplierEvaluationMapper;

    @Autowired
    private SupplierService supplierService;

    @Override
    public Page<SupplierEvaluation> getPages(SupplierEvaluationCondition condition) {
        Page<SupplierEvaluation> page = condition.newPage();
        return supplierEvaluationMapper.getPages(condition, page);
    }

    @Override
    public List<SupplierEvaluation> getList(SupplierEvaluationCondition condition) {
        return supplierEvaluationMapper.getList(condition);
    }

    @Override
    public SupplierEvaluation getDetail(String id) {
        return supplierEvaluationMapper.selectById(id);
    }

    @Override
    public void add(SupplierEvaluation supplierEvaluation) {
        BaseUserDetails baseUserDetails = SecurityHelper.getUser();
        String tenantId = baseUserDetails.getTenantId(); // 租户ID
        String dataHierarchyId = baseUserDetails.getDataHierarchyId(); // 组织层级
        String userId = baseUserDetails.getUserId(); // 用户ID
        supplierEvaluation.setCreateId(userId);
        supplierEvaluation.setUpdateId(userId);
        supplierEvaluation.setTenantId(tenantId);
        supplierEvaluation.setDataHierarchyId(dataHierarchyId);

        if (!this.save(supplierEvaluation)) {
            BizExp.pop("供应商评价信息保存失败");
        }
        updateAvgScore(supplierEvaluation);
    }

    @Override
    public void update(SupplierEvaluation supplierEvaluation) {
        BaseUserDetails baseUserDetails = SecurityHelper.getUser();
        supplierEvaluation.setUpdateId(baseUserDetails.getUserId());
        SupplierEvaluation originalEvaluation = this.getById(supplierEvaluation.getId());
        if (!this.updateById(supplierEvaluation)) {
            BizExp.pop("供应商评价信息更新失败");
        }
        //获取最新数据
        supplierEvaluation=this.getById(supplierEvaluation.getId());
        // 仅当评分修改时更新供应商综合评分
        boolean scoreChanged = isScoreChanged(originalEvaluation, supplierEvaluation);
        if (scoreChanged) {
            //重新计算更新前的供货商评分
            updateAvgScore(originalEvaluation);
            //重新计算更新后的供货商评分
            updateAvgScore(supplierEvaluation);
        }

    }

    @Override
    public boolean delete(String id) {
        SupplierEvaluation supplierEvaluation = this.getById(id);
        if (supplierEvaluation == null) {
            BizExp.pop("供应商评价信息不存在");
        }
        boolean deleteResult = this.update(
                Wrappers.<SupplierEvaluation>lambdaUpdate()
                        .eq(SupplierEvaluation::getId, id)
                        .set(SupplierEvaluation::getEnabled, 0)
        );

        if (!deleteResult) {
            BizExp.pop("删除供应商评价失败");
        }

        //更新供应商的平均分（此时查询到的评价列表已排除刚删除的记录）
        updateAvgScore(supplierEvaluation);

        return true;
    }
    /**
     * 判断评价分数是否修改
     */
    private boolean isScoreChanged(SupplierEvaluation original, SupplierEvaluation updated) {
        // 比较各个评分字段（根据实际字段名调整）
        return !Objects.equals(original.getCreditScore(), updated.getCreditScore()) ||
                !Objects.equals(original.getSupplierId(), updated.getSupplierId()) ||
                !Objects.equals(original.getTimeScore(), updated.getTimeScore()) ||
                !Objects.equals(original.getPriceScore(), updated.getPriceScore()) ||
                !Objects.equals(original.getServScore(), updated.getServScore());
    }
    /**
     * 更新供应商综合评分
     */
    private void updateAvgScore(SupplierEvaluation supplierEvaluation) {
        //更新对应供应商的综合评分
        ProductionSupplier supplier = supplierService.getById(supplierEvaluation.getSupplierId());
        if (supplier == null) {
            // 供应商不存在时，直接抛出明确提示（不执行后续更新）
            BizExp.pop("供应商不存在，无法更新综合评分（供应商ID：" + supplierEvaluation.getSupplierId() + "）");
        }
        BigDecimal avgScore=caculateAvgScore(supplierEvaluation);
        supplier.setAvgScore(avgScore);
        supplierService.updateAvgScore(supplier);
    }
    /**
     * 计算供应商综合评分
     *
     * @param supplierEvaluation
     * @return
     */
    private BigDecimal caculateAvgScore(SupplierEvaluation supplierEvaluation) {
        //查询供应商所有有效评价
        LambdaQueryWrapper<SupplierEvaluation> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(SupplierEvaluation::getSupplierId, supplierEvaluation.getSupplierId())
                .eq(SupplierEvaluation::getEnabled, 1);
        List<SupplierEvaluation> evaluations=supplierEvaluationMapper.selectList(queryWrapper);
        if(evaluations==null || evaluations.isEmpty()){
            return BigDecimal.ZERO;
        }
        double totalCreditScore = 0.0;
        double totalTimeScore = 0.0;
        double totalPriceScore = 0.0;
        double totalServiceScore = 0.0;
        int validCount = 0; // 有效评价数量
        //累加
        for(SupplierEvaluation eval : evaluations){
            totalCreditScore += eval.getCreditScore();
            totalTimeScore += eval.getTimeScore();
            totalPriceScore += eval.getPriceScore();
            totalServiceScore += eval.getServScore();
            validCount++;
        }
        //计算各维度的平均分
        double avgCreditScore = totalCreditScore / validCount;
        double avgTimeScore = totalTimeScore / validCount;
        double avgPriceScore = totalPriceScore / validCount;
        double avgServiceScore = totalServiceScore / validCount;
        //使用枚举权重计算综合评分（1-5分区间）
        double weightedScore =
                avgCreditScore * SupplierEvaluationDimension.CREDIT.getWeight() +
                        avgTimeScore * SupplierEvaluationDimension.TIME.getWeight() +
                        avgPriceScore * SupplierEvaluationDimension.PRICE.getWeight() +
                        avgServiceScore * SupplierEvaluationDimension.SERVICE.getWeight();
        //映射到10.0-99.9分区间
        double finalScore = 10.0 + (weightedScore - 1) * (89.9 / 4);
        return new BigDecimal(finalScore).setScale(1, RoundingMode.HALF_UP);
    }
}
