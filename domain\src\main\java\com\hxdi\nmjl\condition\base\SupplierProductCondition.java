package com.hxdi.nmjl.condition.base;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SupplierProductCondition extends QueryCondition {
    @ApiModelProperty(value = "供应商id")
    private String supplierId;
    @ApiModelProperty(value = "品种id")
    private Integer foodCategoryId;

}
