package com.hxdi.nmjl.vo.condition.specialproduct;


import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 地方特色产品审批查询条件
 */
@ApiModel(description = "地方特色产品审批查询条件")
@Getter
@Setter
public class SpecialProductApprovalCondition extends QueryCondition {

    @ApiModelProperty(value = "特色产品id")
    private String productId;

    @ApiModelProperty(value = "特色产品id列表")
    private List<String> productIds;

    @ApiModelProperty(value = "一级审核状态：0-未审核，1-已审核，2-驳回")
    private Integer approveStatus1;

    @ApiModelProperty(value = "二级审核状态：0-未审核，1-已审核，2-驳回")
    private Integer approveStatus2;

    @ApiModelProperty(value = "三级审核状态：0-未审核，1-已审核，2-驳回")
    private Integer approveStatus3;

    @ApiModelProperty(value = "一级审批人")
    private String approver1;

    @ApiModelProperty(value = "二级审批人")
    private String approver2;

    @ApiModelProperty(value = "三级审批人")
    private String approver3;

    @ApiModelProperty(value = "一级审批时间开始")
    private LocalDateTime approveTime1Start;

    @ApiModelProperty(value = "一级审批时间结束")
    private LocalDateTime approveTime1End;

    @ApiModelProperty(value = "二级审批时间开始")
    private LocalDateTime approveTime2Start;

    @ApiModelProperty(value = "二级审批时间结束")
    private LocalDateTime approveTime2End;

    @ApiModelProperty(value = "三级审批时间开始")
    private LocalDateTime approveTime3Start;

    @ApiModelProperty(value = "三级审批时间结束")
    private LocalDateTime approveTime3End;

    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;
}
    
