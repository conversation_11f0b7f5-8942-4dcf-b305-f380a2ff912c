package com.hxdi.nmjl.service.inventory.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.BaseUserDetails;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.nmjl.condition.inventory.StorageCapacityConversionCondition;
import com.hxdi.nmjl.domain.inventory.StorageCapacity;
import com.hxdi.nmjl.domain.inventory.StorageCapacityConversion;
import com.hxdi.nmjl.mapper.inventory.StorageCapacityConversionMapper;
import com.hxdi.nmjl.mapper.inventory.StorageCapacityMapper;
import com.hxdi.nmjl.service.inventory.StorageCapacityConversionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * @Data 2025/7/19 15:40
 * @Description: 库容转换记录服务实现类
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class StorageCapacityConversionServiceImpl extends BaseServiceImpl<StorageCapacityConversionMapper, StorageCapacityConversion> implements StorageCapacityConversionService {
    @Autowired
    public StorageCapacityMapper storageCapacityMapper;

    @Override
    public Page<StorageCapacityConversion> getPages(StorageCapacityConversionCondition condition){
        Page<StorageCapacityConversion> page = condition.newPage();
        return baseMapper.getPages(condition,page);
    }
    @Override
    public List<StorageCapacityConversion> getList(StorageCapacityConversionCondition condition){
        return baseMapper.getList(condition);
    }
    @Override
    public StorageCapacityConversion getDetail(String Id) {
        StorageCapacityConversion storageCapacityConversion = baseMapper.selectById(Id);
        return storageCapacityConversion;
    }

    @Override
    public void add(StorageCapacityConversion storageCapacityConversion) {
        // 设置基础信息
        BaseUserDetails baseUserDetails = SecurityHelper.getUser();
        String tenantId = baseUserDetails.getTenantId();
        String dataHierarchyId = baseUserDetails.getDataHierarchyId();
        storageCapacityConversion.setTenantId(tenantId);
        storageCapacityConversion.setDataHierarchyId(dataHierarchyId);
        //判断可用容量
        StorageCapacity storageCapacity = storageCapacityMapper.selectById(storageCapacityConversion.getStorageCapId());
        if(storageCapacity.getAvailableCap().compareTo(storageCapacityConversion.getAllocateCap())<0){
            BizExp.pop("可用容量不足");
        }
        storageCapacityConversion.setUsedCap(storageCapacity.getUsedCap());
        storageCapacityConversion.setAvailableCap(storageCapacity.getAvailableCap());
        if(!this.save(storageCapacityConversion)){
            BizExp.pop("保存转换记录失败");
        }
    }

    @Override
    public void update(StorageCapacityConversion storageCapacityConversion) {
        StorageCapacity storageCapacity = storageCapacityMapper.selectById(storageCapacityConversion.getStorageCapId());
        if(storageCapacity.getAvailableCap().compareTo(storageCapacityConversion.getAllocateCap())<0){
            BizExp.pop("可用容量不足");
        }
        if(!this.updateById(storageCapacityConversion)){
            BizExp.pop("更新转换记录失败");
        }
    }

    @Override
    public boolean delete(String Id){
        StorageCapacityConversion storageCapacityConversion = this.getById(Id);
        if(storageCapacityConversion==null){
            BizExp.pop("转换记录不存在");
        }
        if(storageCapacityConversion.getApproveStatus()!=null){
            BizExp.pop("已提交的数据，不能删除");
        }
        this.update(Wrappers.<StorageCapacityConversion>lambdaUpdate().eq(StorageCapacityConversion::getId, Id).set(StorageCapacityConversion::getEnabled, 0));
        return true;
    }

    @Override
    public void approve(String id, String approveOpinion) {
        //审批通过
        changeApproveStatus(id, 1, approveOpinion);
    }

    @Override
    public void submit(String id) {
        // 审核状态
        changeApproveStatus(id, 0, null);
    }

    @Override
    public void reject(String id, String approveOpinion) {
        changeApproveStatus(id, 2, approveOpinion);
    }

    @Override
    public void recycle(String id) {
        //回收为政策性占用
        StorageCapacityConversion storageCapacityConversion = baseMapper.selectById(id);
        storageCapacityConversion.setState(3);
        baseMapper.updateById(storageCapacityConversion);
        //增加可用量
        StorageCapacity storageCapacity = storageCapacityMapper.selectById(storageCapacityConversion.getStorageCapId());
        storageCapacity.setAvailableCap(storageCapacity.getAvailableCap().add(storageCapacityConversion.getAllocateCap()));
        storageCapacity.setUsedCap(storageCapacity.getUsedCap().subtract(storageCapacityConversion.getAllocateCap()));
        storageCapacityMapper.updateById(storageCapacity);
    }

    private void changeApproveStatus(String id, int approveStatus, String approveOpinion) {
        StorageCapacityConversion storageCapacityConversion = baseMapper.selectById(id);
        storageCapacityConversion.setApproveStatus(approveStatus);
        StorageCapacity storageCapacity = storageCapacityMapper.selectById(storageCapacityConversion.getStorageCapId());
        if (approveStatus == 1 || approveStatus == 2) {
            storageCapacityConversion.setApprover(SecurityHelper.obtainUser().getNickName());
            storageCapacityConversion.setApproveTime(new Date());
            storageCapacityConversion.setApproveOpinion(approveOpinion);
            //更新库容审批信息
            storageCapacity.setApproveStatus(approveStatus);
            storageCapacity.setApproveTime(new Date());
            storageCapacity.setApproveOpinion(approveOpinion);
            storageCapacity.setApprover(SecurityHelper.obtainUser().getNickName());
            //审批通过更新库容
            if(approveStatus==1){
                //审批通过更新为租用
                storageCapacityConversion.setState(2);
                storageCapacity.setUsedCap(storageCapacity.getUsedCap().add(storageCapacityConversion.getAllocateCap()));
                storageCapacity.setAvailableCap(storageCapacity.getAvailableCap().subtract(storageCapacityConversion.getAllocateCap()));
                storageCapacityMapper.updateById(storageCapacity);
            }
        }
        baseMapper.updateById(storageCapacityConversion);
    }
}

