<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.base.SupplierEvaluationMapper">

    <sql id="Base_Column_List">
        ID, SUPPLIER_ID, COMMENTS, SERV_SCORE, PRICE_SCORE, TIME_SCORE,
        CREDIT_SCORE, EVALUATOR, ENABLED, ATTACHMENTS,
        CREATE_TIME, UPDATE_TIME, CREATE_ID, UPDATE_ID,
        TENANT_ID, DATA_HIERARCHY_ID, SUPPLIER_NAME
    </sql>

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.base.SupplierEvaluation">
        <id column="ID" property="id"/>
        <result column="SUPPLIER_ID" property="supplierId"/>
        <result column="COMMENTS" property="comments"/>
        <result column="SERV_SCORE" property="servScore"/>
        <result column="PRICE_SCORE" property="priceScore"/>
        <result column="TIME_SCORE" property="timeScore"/>
        <result column="CREDIT_SCORE" property="creditScore"/>
        <result column="EVALUATOR" property="evaluator"/>
        <result column="ENABLED" property="enabled"/>
        <result column="ATTACHMENTS" property="attachments"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="UPDATE_TIME" property="updateTime"/>
        <result column="CREATE_ID" property="createId"/>
        <result column="UPDATE_ID" property="updateId"/>
        <result column="TENANT_ID" property="tenantId"/>
        <result column="DATA_HIERARCHY_ID" property="dataHierarchyId"/>
        <result column="SUPPLIER_NAME" property="supplierName"/>
    </resultMap>

    <select id="getList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM B_SUPPLIER_EVALUATION
        <where>
            enabled = 1
            <if test="condition.supplierName != null and condition.supplierName != ''">
                AND SUPPLIER_NAME LIKE CONCAT('%', #{condition.supplierName}, '%')
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <select id="getPages" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM B_SUPPLIER_EVALUATION
        <where>
            enabled = 1
            <if test="condition.supplierName != null and condition.supplierName != ''">
                AND SUPPLIER_NAME LIKE CONCAT('%', #{condition.supplierName}, '%')
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

</mapper>
