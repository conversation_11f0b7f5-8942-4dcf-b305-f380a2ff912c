package com.hxdi.nmjl.condition.inventory;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

public class StorageCapacityConversionCondition extends QueryCondition {
    @ApiModelProperty(value = "客户名称")
    private String clientName;

    //调配日期
    @ApiModelProperty(value = "开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "业务状态：1-闲置，2-租用，3-政策性占用")
    private Integer state;
}
