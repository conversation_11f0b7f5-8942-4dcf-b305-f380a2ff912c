package com.hxdi.nmjl.condition.msg;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @program: nmjl-service
 * @description: 告警信息查询条件
 * @author: 王贝强
 * @create: 2025-07-09 11:26
 */
@Getter
@Setter
@ApiModel(description = "告警信息查询条件")
public class WarningInfoCondition extends QueryCondition {

    @ApiModelProperty(value = "告警类型：1-库存预警，2-设备预警，3-其他预警")
    private String warningType;

    @ApiModelProperty(value = "库点ID ','分隔")
    private String storeId;

    @ApiModelProperty(value = "仓房ID")
    private String stId;

    @ApiModelProperty(value = "货位ID")
    private String locId;

    /**
     * 处理状态:0-未处理，1-已处理
     */
    @ApiModelProperty(value = "处理状态:0-未处理，1-已处理")
    private Integer disposeState;

    @ApiModelProperty(value = "处理人id")
    private String userId;


    //-------------大屏参数---------------

    @ApiModelProperty(value = "大屏参数：地区编码 注意：此参数与storeId二选一")
    private String areaCode;
}
