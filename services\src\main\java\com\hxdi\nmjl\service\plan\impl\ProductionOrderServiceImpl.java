package com.hxdi.nmjl.service.plan.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.model.DataType;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.BaseUserDetails;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.plan.ProductionOrderCondition;
import com.hxdi.nmjl.domain.inout.InoutDetail;
import com.hxdi.nmjl.domain.plan.ProductionOrder;
import com.hxdi.nmjl.domain.plan.ProductionOrderItem;
import com.hxdi.nmjl.domain.plan.ProductionOrderTrace;
import com.hxdi.nmjl.feign.SystemNumberRuleClient;
import com.hxdi.nmjl.mapper.plan.ProductionOrderMapper;
import com.hxdi.nmjl.service.inventory.LocationCardService;
import com.hxdi.nmjl.service.plan.ContractInfoService;
import com.hxdi.nmjl.service.plan.ProductionOrderItemService;
import com.hxdi.nmjl.service.plan.ProductionOrderService;
import com.hxdi.nmjl.service.plan.ProductionOrderTraceService;
import com.hxdi.system.client.model.dto.BusinessCode;
import com.hxdi.system.client.model.dto.BusinessCodeParams;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Transactional(rollbackFor = Exception.class)
public class ProductionOrderServiceImpl extends BaseServiceImpl<ProductionOrderMapper, ProductionOrder> implements ProductionOrderService {

    @Resource
    private SystemNumberRuleClient systemNumberRuleClient;

    @Resource
    private ContractInfoService contractInfoService;

    @Resource
    private ProductionOrderItemService productionOrderItemService;

    @Resource
    private ProductionOrderTraceService productionOrderTraceService;

    @Override
    public ProductionOrder getDetail(String id) {
        ProductionOrder productionOrder = baseMapper.selectById(id);
        productionOrder.setDetailList(productionOrderItemService.listByOrderId(id));
        return productionOrder;
    }

    @Override
    public List<ProductionOrder> listV1(ProductionOrderCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public List<ProductionOrder> listV2(ProductionOrderCondition condition) {
        List<ProductionOrder> orderList = baseMapper.selectListV2(condition);
        if (orderList.isEmpty()) {
            return orderList;
        }
        List<String> idList = orderList.stream().map(ProductionOrder::getId).collect(Collectors.toList());
        List<ProductionOrderItem> itemList = productionOrderItemService.listByIdList(idList);
        if (!itemList.isEmpty()) {
            Map<String, List<ProductionOrderItem>> itemMap = itemList.stream().collect(Collectors.groupingBy(ProductionOrderItem::getOrderId));
            orderList.forEach(order -> order.setDetailList(itemMap.get(order.getId())));
        }
        return orderList;
    }

    @Override
    public Page<ProductionOrder> pageV1(ProductionOrderCondition condition) {
        Page<ProductionOrder> page = condition.newPage();
        return baseMapper.selectPageV1(page, condition);
    }

    @Override
    public void createV1(ProductionOrder productionOrder) {
        //检查批次号是否重复
        List<ProductionOrder> list = baseMapper.selectList(Wrappers.<ProductionOrder>lambdaQuery()
                .eq(ProductionOrder::getBatchNo, productionOrder.getBatchNo())
                .eq(ProductionOrder::getEnabled, 1));

        if(!list.isEmpty()){
            throw new BaseException("当前批次号已存在，请勿重复使用！");
        }

        //生成订单编码
        BusinessCodeParams params = new BusinessCodeParams();
        params.setCode("PROD_ORDER_CODE");
        params.setDt(DataType.STRING);
        BusinessCode businessCode = systemNumberRuleClient.getNumber(params).getData();
        productionOrder.setOrderCode((String) businessCode.getValue());

        //上级单位创建的订单，将权限字段设置为对应的库点
        BaseUserDetails user = SecurityHelper.getUser();
        if(!Objects.equals(user.getOrganId(), productionOrder.getStoreId())){
            productionOrder.setDataHierarchyId(productionOrder.getStoreId());
        }

        productionOrder.setState(0); // 默认状态：未执行

        //保存订单信息
        this.save(productionOrder);
        //保存详情
        if (productionOrder.getDetailList() != null) {
            productionOrder.getDetailList().forEach(detail -> detail.setOrderId(productionOrder.getId()));
            productionOrderItemService.saveBatch(productionOrder.getDetailList());
        }
    }

    @Override
    public void updateV1(ProductionOrder productionOrder) {
        if (productionOrder.getState() != 0) {
            throw new BaseException("当前订单已提交，无法修改！");
        }

        //检查批次号是否重复
        List<ProductionOrder> list = baseMapper.selectList(Wrappers.<ProductionOrder>lambdaQuery()
                .eq(ProductionOrder::getBatchNo, productionOrder.getBatchNo())
                .eq(ProductionOrder::getEnabled, 1)
                .ne(ProductionOrder::getId, productionOrder.getId()));

        if(!list.isEmpty()){
            throw new BaseException("当前批次号已存在，请勿重复使用！");
        }


        this.updateById(productionOrder);
        if (productionOrder.getDetailList() != null) {
            productionOrderItemService.removeV1(productionOrder.getId());
            productionOrder.getDetailList().forEach(detail -> detail.setOrderId(productionOrder.getId()));
            productionOrderItemService.saveBatch(productionOrder.getDetailList());
        }
    }

    public void removeV1(String id) {
        ProductionOrder order = this.getById(id);
        if (order.getState() != 0) {
            throw new BaseException("当前订单已提交，无法删除！");
        }
        //逻辑删除主表
        ProductionOrder productionOrder = new ProductionOrder();
        productionOrder.setId(id);
        productionOrder.setEnabled(0);
        this.updateById(productionOrder);
    }

    @Override
    public void submitV1(String id) {
        ProductionOrder productionOrder = this.getById(id);
        if (productionOrder.getState() != 0) {
            throw new BaseException("当前订单已提交，无法再次提交！");
        }
        productionOrder.setState(1);
        this.updateById(productionOrder);
    }

    @Override
    public void fillOrder(List<ProductionOrderTrace> traceList) {
        // 参数校验
        if (traceList == null || traceList.isEmpty()) {
            return;
        }

        String orderId = traceList.get(0).getOrderId();

        ProductionOrder productionOrder = this.getById(orderId);
        if (productionOrder == null) {
            throw new BaseException("未找到对应的订单数据！");
        }

        List<ProductionOrderItem> detailList = productionOrderItemService.listByOrderId(orderId);
        if (detailList == null || detailList.isEmpty()) {
            throw new BaseException("未找到对应的订单明细数据！");
        }

        // 构建跟踪数据映射
        Map<String, ProductionOrderTrace> traceMap = traceList.stream()
                .collect(Collectors.toMap(ProductionOrderTrace::getOrderItemId, trace -> trace));

        //去除不需要更新的详情
        detailList.removeIf(detail -> traceMap.get(detail.getId()) == null);

        // 更新订单明细并计算总数量
        BigDecimal totalQty = detailList.stream()
                .map(detail -> updateDetailWithTrace(detail, traceMap.get(detail.getId())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        productionOrder.setCompletedQty(totalQty);
        if (productionOrder.getPlanQty().compareTo(totalQty) <= 0) {
            productionOrder.setState(2);
        }

        // 写入生产订单的跟踪数据
        List<ProductionOrderTrace> traceUpdateList = traceMap.values().stream().filter(Objects::nonNull).collect(Collectors.toList());
        productionOrderTraceService.generateTrace(traceUpdateList);

        // 批量更新
        updateOrderAndDetails(productionOrder, detailList);

        productionOrder.setDetailList(detailList);
        contractInfoService.updateProcess(traceUpdateList);
    }

    @Override
    public void generateOrderStatistics(List<InoutDetail> inoutDetailList) {

    }

    private BigDecimal updateDetailWithTrace(ProductionOrderItem detail, ProductionOrderTrace trace) {
        if (trace != null) {
            if (detail.getCompletedPackQty() == null) {
                detail.setCompletedPackQty(0);
            }
            if (detail.getCompletedQty() == null) {
                detail.setCompletedQty(BigDecimal.ZERO);
            }

            if (trace.getTotalPackQty() == null || trace.getTotalQty() == null) {
                throw new BaseException("累计生产件数和累计完成数量不能为空！");
            }

            detail.setCompletedPackQty(detail.getCompletedPackQty() + trace.getTotalPackQty());
            detail.setCompletedQty(detail.getCompletedQty().add(trace.getTotalQty()));

            BaseUserDetails user = SecurityHelper.obtainUser();
            trace.setReporter(user.getUserId());
            trace.setReportTime(new Date());

            if (detail.getOrderQty().compareTo(detail.getCompletedQty()) <= 0) {
                trace.setBatchState(1);
            }

            // 日期处理
            LocalDate currentDate = trace.getProductDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            if (detail.getProductDate() != null && CommonUtils.isNotBlank(detail.getProductDate())) {
                String[] productDate = detail.getProductDate().split("~");
                detail.setProductDate(productDate[0] + "~" + currentDate);
            } else {
                detail.setProductDate(currentDate.toString());
            }
        }
        return detail.getCompletedQty();
    }

    private void updateOrderAndDetails(ProductionOrder order, List<ProductionOrderItem> detailList) {
        // 更新订单总完成数量及状态
        this.updateById(order);

        // 批量更新订单明细
        productionOrderItemService.updateBatchById(detailList);
    }

}
