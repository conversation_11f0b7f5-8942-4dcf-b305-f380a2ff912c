package com.hxdi.nmjl.service.inventory.impl;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.constants.StrPool;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.model.DataType;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.inventory.InventoryAllocationCondition;
import com.hxdi.nmjl.domain.inout.opt.AdditiveConsuming;
import com.hxdi.nmjl.domain.inventory.InventoryAllocation;
import com.hxdi.nmjl.domain.inventory.InventoryAllocationDetail;
import com.hxdi.nmjl.domain.inventory.InventoryCheck;
import com.hxdi.nmjl.enums.TaskStatus;
import com.hxdi.nmjl.feign.SystemNumberRuleClient;
import com.hxdi.nmjl.mapper.inventory.InventoryAllocationMapper;
import com.hxdi.nmjl.service.inventory.InventoryAllocationDetailService;
import com.hxdi.nmjl.service.inventory.InventoryAllocationService;
import com.hxdi.system.client.model.dto.BusinessCode;
import com.hxdi.system.client.model.dto.BusinessCodeParams;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【B_INVENTORY_ALLOCATION(库存调配管理)】的数据库操作Service实现
* @createDate 2025-07-31 09:45:14
*/
@Service
public class InventoryAllocationServiceImpl extends BaseServiceImpl<InventoryAllocationMapper, InventoryAllocation>
    implements InventoryAllocationService {
    @Resource
    private SystemNumberRuleClient systemNumberRuleClient;

    @Resource
    private InventoryAllocationDetailService inventoryAllocationDetailService;


    @Override
    public void createOrUpdate(InventoryAllocation inventoryAllocation) {
        if(CommonUtils.isNotEmpty(inventoryAllocation.getId())){
            if(!Objects.equals(TaskStatus.UNEXECUTED.getCode(), inventoryAllocation.getAllocateState())){
                throw new BaseException("无法更新已经开始作业的调度计划！");
            }
            baseMapper.updateById(inventoryAllocation);
            if(inventoryAllocation.getDetailList() != null){
                //批量删除调度计划详情
                inventoryAllocationDetailService.removeV1(inventoryAllocation.getId());
                inventoryAllocation.getDetailList().forEach(detail->{
                    detail.setAllocateId(inventoryAllocation.getId());
                    detail.setDataHierarchyId(inventoryAllocation.getDataHierarchyId());
                    detail.setTenantId(inventoryAllocation.getTenantId());
                });
                //重新插入新的调度计划详情
                inventoryAllocationDetailService.saveBatch(inventoryAllocation.getDetailList());
            }
        }else {
            // 生成调度计划编号
            BusinessCodeParams params = new BusinessCodeParams();
            params.setCode("INVENTORY_ALLOCATION_CODE");
            params.setDt(DataType.STRING);
            BusinessCode businessCode = systemNumberRuleClient.getNumber(params).getData();
            inventoryAllocation.setAllocateCode((String) businessCode.getValue());
            //新增调度计划
            inventoryAllocation.setAllocateState(TaskStatus.UNEXECUTED.getCode());
            this.save(inventoryAllocation);
            //新增调度计划详情
            inventoryAllocation.getDetailList().forEach(detail ->{
                detail.setAllocateId(inventoryAllocation.getId());
                detail.setDataHierarchyId(inventoryAllocation.getDataHierarchyId());
                detail.setTenantId(inventoryAllocation.getTenantId());
            });
            inventoryAllocationDetailService.saveBatch(inventoryAllocation.getDetailList());
        }
    }

    @Override
    public void removeById(String id) {
        InventoryAllocation task = baseMapper.selectById(id);
        if(CommonUtils.isNotEmpty(task)){
            if(!Objects.equals(TaskStatus.UNEXECUTED.getCode(), task.getAllocateCode())){
                throw new BaseException("无法删除已经开始作业的调度计划！");
            }
            //删除调度计划(逻辑删除)
            task.setEnabled(StrPool.State.DISABLE);
            baseMapper.updateById(task);
        }
    }

    @Override
    public InventoryAllocation getMore(String id) {
        InventoryAllocation task;
        task = baseMapper.selectById(id);
        if(CommonUtils.isEmpty(task)){
            throw new BaseException("查询为空对象,查询错误!");
        }
        //拿到详情列表
        List<InventoryAllocationDetail> inventoryAllocationDetailList = inventoryAllocationDetailService.getListV1(id);
        task.setDetailList(inventoryAllocationDetailList);
        return task;
    }



    @Override
    @DataPermission
    public List<InventoryAllocation> listV1(InventoryAllocationCondition condition) {
        return baseMapper.listV1(condition);
    }

    @Override
    public Page<InventoryAllocation> PageV1(InventoryAllocationCondition condition) {
        Page<InventoryAllocation> page = condition.newPage();
        return baseMapper.PageV1(condition, page);
    }

    @Override
    public void submitV1(String id) {
        InventoryAllocation productionOrder = this.getById(id);
        if (productionOrder.getAllocateState() != 0) {
            throw new BaseException("当前订单已提交，无法再次提交！");
        }
        productionOrder.setAllocateState(1);
        productionOrder.setApproveStatus(0);
        this.updateById(productionOrder);
    }

    @Override
    public void approve(String id,String approveOpinion) {
        // 审批状态：0-未审批，1-已通过，2-已驳回
        changeApproveStatus(id, 1, approveOpinion);
    }
    private void changeApproveStatus(String id, int approveStatus, String approveOpinion) {
        InventoryAllocation inventoryAllocation = new InventoryAllocation();
        inventoryAllocation.setId(id);
        inventoryAllocation.setApproveStatus(approveStatus);
        // 修改提交状态为已提交
        inventoryAllocation.setAllocateState(2);
        inventoryAllocation.setApprover(SecurityHelper.obtainUser().getNickName());
        inventoryAllocation.setApproveTime(new Date());
        if(approveStatus==2){
            inventoryAllocation.setApproveOpinion(approveOpinion);
        }
        baseMapper.updateById(inventoryAllocation);
    }
}




