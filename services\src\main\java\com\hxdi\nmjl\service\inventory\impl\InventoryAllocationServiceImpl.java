package com.hxdi.nmjl.service.inventory.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.constants.StrPool;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.model.DataType;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.cache.CacheProvider;
import com.hxdi.nmjl.condition.inventory.InventoryAllocationCondition;
import com.hxdi.nmjl.domain.base.Catalog;
import com.hxdi.nmjl.domain.inventory.Inventory;
import com.hxdi.nmjl.domain.inventory.InventoryAllocation;
import com.hxdi.nmjl.domain.inventory.InventoryAllocationDetail;
import com.hxdi.nmjl.enums.TaskStatus;
import com.hxdi.nmjl.feign.SystemNumberRuleClient;
import com.hxdi.nmjl.mapper.inventory.InventoryAllocationMapper;
import com.hxdi.nmjl.service.inout.InoutDetailService;
import com.hxdi.nmjl.service.inventory.InventoryAllocationDetailService;
import com.hxdi.nmjl.service.inventory.InventoryAllocationService;
import com.hxdi.nmjl.service.inventory.InventoryService;
import com.hxdi.nmjl.utils.RedisKeys;
import com.hxdi.system.client.model.dto.BusinessCode;
import com.hxdi.system.client.model.dto.BusinessCodeParams;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【B_INVENTORY_ALLOCATION(库存调配管理)】的数据库操作Service实现
* @createDate 2025-07-31 09:45:14
*/
@Service
public class InventoryAllocationServiceImpl extends BaseServiceImpl<InventoryAllocationMapper, InventoryAllocation>
    implements InventoryAllocationService {
    @Resource
    private SystemNumberRuleClient systemNumberRuleClient;

    @Resource
    private InventoryService inventoryService;

    @Resource
    private InventoryAllocationService inventoryAllocationService;

    @Resource
    private InventoryAllocationDetailService inventoryAllocationDetailService;


    @Override
    public void createOrUpdate(InventoryAllocation inventoryAllocation) {
        if(CommonUtils.isNotEmpty(inventoryAllocation.getId())){
            if(!Objects.equals(TaskStatus.UNEXECUTED.getCode(), inventoryAllocation.getAllocateState())){
                throw new BaseException("无法更新已经开始作业的调度计划！");
            }
            baseMapper.updateById(inventoryAllocation);
            inventoryAllocationDetailService.removeById(inventoryAllocation.getId());
            inventoryAllocationDetailService.save(inventoryAllocation.getInventoryAllocationDetail());
        }else {
            // 生成调度计划编号
            BusinessCodeParams params = new BusinessCodeParams();
            params.setCode("INVENTORY_ALLOCATION_CODE");
            params.setDt(DataType.STRING);
            BusinessCode businessCode = systemNumberRuleClient.getNumber(params).getData();
            inventoryAllocation.setAllocateCode((String) businessCode.getValue());
            //新增调度计划
            baseMapper.insert(inventoryAllocation);
            //新增调度计划详情
            inventoryAllocationDetailService.save(inventoryAllocation.getInventoryAllocationDetail());
        }
    }

    @Override
    public void removeById(String id) {
        InventoryAllocation task = baseMapper.selectById(id);
        if(CommonUtils.isNotEmpty(task)){
            if(!Objects.equals(TaskStatus.UNEXECUTED.getCode(), task.getAllocateCode())){
                throw new BaseException("无法删除已经开始作业的调度计划！");
            }
            //删除调度计划详情
            inventoryAllocationDetailService.removeById(task.getInventoryAllocationDetail().getId());
            //删除调度计划(逻辑删除)
            task.setEnabled(StrPool.State.DISABLE);
            baseMapper.updateById(task);
        }
    }

    @Override
    public InventoryAllocation getMore(String id) {
        InventoryAllocation task;
        task = baseMapper.selectById(id);
        if(CommonUtils.isEmpty(task)){
            throw new BaseException("查询为空对象,查询错误!");
        }
        task.setInventoryAllocationDetail(inventoryAllocationDetailService.getById(task.getId()));
        return task;
    }



    @Override
    @DataPermission
    public List<InventoryAllocation> listV1(InventoryAllocationCondition condition) {
        return baseMapper.listV1(condition);
    }

    @Override
    public Page<InventoryAllocation> PageV1(InventoryAllocationCondition condition) {
        Page<InventoryAllocation> page = condition.newPage();
        return baseMapper.PageV1(condition, page);
    }

    @Override
    public void submitV1(String id) {
        InventoryAllocation productionOrder = this.getById(id);
        if (productionOrder.getAllocateState() != 0) {
            throw new BaseException("当前订单已提交，无法再次提交！");
        }
        productionOrder.setAllocateState(1);
        this.updateById(productionOrder);
        //提交以后
    }
}




