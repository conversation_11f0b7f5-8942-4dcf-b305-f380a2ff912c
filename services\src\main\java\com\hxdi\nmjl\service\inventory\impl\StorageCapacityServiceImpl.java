package com.hxdi.nmjl.service.inventory.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.BaseUserDetails;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.nmjl.condition.inventory.StorageCapacityCondition;
import com.hxdi.nmjl.domain.inventory.StorageCapacity;
import com.hxdi.nmjl.mapper.inventory.StorageCapacityMapper;
import com.hxdi.nmjl.service.inventory.StorageCapacityConversionService;
import com.hxdi.nmjl.service.inventory.StorageCapacityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * @Data 2025/7/19 15:40
 * @Description: 库容管理实现类
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class StorageCapacityServiceImpl extends BaseServiceImpl<StorageCapacityMapper, StorageCapacity> implements StorageCapacityService {
    @Autowired
    private StorageCapacityConversionService storageCapacityConversionService;

    @Override
    public Page<StorageCapacity> getPages(StorageCapacityCondition condition){
        Page<StorageCapacity> page = condition.newPage();
        return baseMapper.getPages(condition,page);
    }

    @Override
    public List<StorageCapacity> getList(StorageCapacityCondition condition){
        return baseMapper.getList(condition);
    }

    @Override
    public StorageCapacity getDetail(String Id) {
        StorageCapacity storageCapacity = baseMapper.selectById(Id);
        return storageCapacity;
    }

    @Override
    public void add(StorageCapacity storageCapacity) {
        // 设置基础信息
        BaseUserDetails baseUserDetails = SecurityHelper.getUser();
        String userId = baseUserDetails.getUserId();
        String tenantId = baseUserDetails.getTenantId();
        String dataHierarchyId = baseUserDetails.getDataHierarchyId();
        storageCapacity.setCreateId(userId);
        storageCapacity.setUpdateId(userId);
        storageCapacity.setTenantId(tenantId);
        storageCapacity.setDataHierarchyId(dataHierarchyId);
        if(storageCapacity.getMaxCap().compareTo(storageCapacity.getUsedCap())<0){
            BizExp.pop("最大容量不能小于已使用容量");
        }
        //计算可用容量
        BigDecimal availableCap = storageCapacity.getMaxCap().subtract(storageCapacity.getUsedCap());
        storageCapacity.setAvailableCap(availableCap);
        if (!this.save(storageCapacity)) {
            BizExp.pop("库容信息保存失败");
        }
    }

    @Override
    public void update(StorageCapacity storageCapacity) {
        if(storageCapacity.getMaxCap().compareTo(storageCapacity.getUsedCap())<0){
            BizExp.pop("最大容量不能小于已使用容量");
        }
        //原仓容和最新仓容
        StorageCapacity storageCapacityOld=baseMapper.selectById(storageCapacity.getId());
        BigDecimal oldMaxCap = storageCapacityOld.getMaxCap();
        BigDecimal newMaxCap = storageCapacity.getMaxCap();
        BigDecimal oldUsedCap = storageCapacityOld.getUsedCap();
        BigDecimal newUsedCap = storageCapacity.getUsedCap();
        //原可利用仓容
        BigDecimal oldAvailableCap=storageCapacityOld.getAvailableCap();
        if(oldMaxCap.compareTo(newMaxCap)!=0|| oldUsedCap.compareTo(newUsedCap)!=0){
            // 计算变动量
            BigDecimal maxCapChange = newMaxCap.subtract(oldMaxCap);
            BigDecimal usedCapChange = newUsedCap.subtract(oldUsedCap);
            // 新可利用库容 = 原可利用库容 + 最大库容增加量 - 已使用库容增加量
            BigDecimal newAvailableCap = oldAvailableCap.add(maxCapChange).subtract(usedCapChange);
            if (newAvailableCap.compareTo(BigDecimal.ZERO) < 0) {
                BizExp.pop("计算后可利用库容不能为负数");
            }
            storageCapacity.setAvailableCap(newAvailableCap);
        }
        if (!this.updateById(storageCapacity)) {
            BizExp.pop("库容信息更新失败");
        }
    }

    @Override
    public boolean delete(String Id) {
        StorageCapacity storageCapacity = this.getById(Id);
        if (storageCapacity == null) {
            BizExp.pop("库容信息不存在");
        }
        if (storageCapacity.getApproveStatus() != null) {
            BizExp.pop("已提交的数据无法删除!");
        }
        this.update(Wrappers.<StorageCapacity>lambdaUpdate().eq(StorageCapacity::getId, Id).set(StorageCapacity::getEnabled, 0));
        return true;
    }

}
