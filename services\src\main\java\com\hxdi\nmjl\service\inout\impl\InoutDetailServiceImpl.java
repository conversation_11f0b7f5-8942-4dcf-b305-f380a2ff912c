package com.hxdi.nmjl.service.inout.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.model.DataType;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.nmjl.condition.inout.InoutDetailCondition;
import com.hxdi.nmjl.domain.inout.InoutDetail;
import com.hxdi.nmjl.domain.inout.InoutItem;
import com.hxdi.nmjl.domain.inout.InoutTask;
import com.hxdi.nmjl.feign.SystemNumberRuleClient;
import com.hxdi.nmjl.linker.InventoryLinker;
import com.hxdi.nmjl.mapper.inout.InoutDetailMapper;
import com.hxdi.nmjl.service.inout.InoutDetailService;
import com.hxdi.nmjl.service.inout.InoutItemService;
import com.hxdi.nmjl.service.inout.InoutTaskService;
import com.hxdi.system.client.model.dto.BusinessCode;
import com.hxdi.system.client.model.dto.BusinessCodeParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 出入库记录服务实现
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/15 15:00
 */
@Transactional(rollbackFor = Exception.class)
@Slf4j
@Service
public class InoutDetailServiceImpl extends BaseServiceImpl<InoutDetailMapper, InoutDetail> implements InoutDetailService {

    @Autowired
    private InoutTaskService inoutTaskService;

    @Autowired
    private InoutItemService inoutItemService;

    @Autowired
    @Lazy
    private InventoryLinker inventoryLinker;

    @Autowired
    private SystemNumberRuleClient systemNumberRuleClient;

    @Override
    public void create(InoutDetail inoutDetail) {
        BusinessCodeParams params = new BusinessCodeParams();
        params.setCode("INOUT_DETAIL_CODE");
        params.setDt(DataType.STRING);
        BusinessCode businessCode = systemNumberRuleClient.getNumber(params).getData();
        inoutDetail.setInoutCode((String) businessCode.getValue());

        InoutTask inoutTask = inoutTaskService.getByCode(inoutDetail.getTaskCode());
        inoutDetail.setStoreId(SecurityHelper.obtainUser().getOrganId());
        inoutDetail.setStoreName(SecurityHelper.obtainUser().getOrganName());
        inoutDetail.setOrgId(SecurityHelper.obtainUser().getPid());
        inoutDetail.setReserveLevel(inoutTask.getReserveLevel());
        inoutDetail.setInoutType(inoutTask.getInoutType());
        inoutDetail.setInoutBizType(inoutTask.getInoutBizType());
        inoutDetail.setClientId(inoutTask.getClientId());
        inoutDetail.setClientName(inoutTask.getClientName());
        inoutDetail.setBatchNum(inoutTask.getBatchNum());
        inoutDetail.setOptFlg(0);
        baseMapper.insert(inoutDetail);

        inoutDetail.getDetailList().forEach(item -> item.setInoutDetailId(inoutDetail.getId()));

        inoutItemService.saveBatch(inoutDetail.getDetailList());

        afterCommitDone(inoutDetail);
    }

    @Override
    public void updating(InoutDetail inoutDetail) {
        baseMapper.updateById(inoutDetail);
        inoutItemService.updateBatchById(inoutDetail.getDetailList());

        afterCommitDone(inoutDetail);
    }

    @Override
    public void commit(String id) {
        InoutDetail inoutDetail = baseMapper.selectById(id);
        inoutDetail.setOptFlg(1);
        baseMapper.updateById(inoutDetail);

        inoutDetail.setDetailList(inoutItemService.getListByMainId(id));
        afterCommitDone(inoutDetail);
    }

    @Override
    public void remove(String id) {
        InoutDetail inoutDetail = baseMapper.selectById(id);
        if (inoutDetail.getOptFlg() == 1) {
            BizExp.pop("该单据已提交，不能删除");
        }

        baseMapper.deleteById(id);
    }


    @Override
    public InoutDetail detail(String id) {
        InoutDetail detail = baseMapper.selectById(id);
        detail.setDetailList(inoutItemService.getListByMainId(id));
        return detail;
    }

    @Override
    public Page<InoutDetail> getPage(InoutDetailCondition condition) {
        Page<InoutDetail> page = condition.newPage();
        baseMapper.selectPageV1(page, condition);

        page.getRecords().forEach(detail -> {
            InoutItem item = inoutItemService.getStatInfo(detail.getId());
            detail.setCatalogName(item.getCatalogName());
            detail.setGrade(item.getGrade());
            detail.setSpecification(item.getSpecification());
            detail.setTotalQty(item.getQty());
        });
        return page;
    }

    @Override
    public List<InoutDetail> getList(InoutDetailCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public String getOrderId(String id) {
        InoutDetail detail = baseMapper.selectOne(Wrappers.<InoutDetail>lambdaQuery().select(InoutDetail::getTaskCode).eq(InoutDetail::getId, id));
        if (detail == null) {
            return null;
        }
        InoutTask task = inoutTaskService.getByCode(detail.getTaskCode());
        if (task == null) {
            return null;
        }
        return task.getOrderId();
    }

    /**
     * 提交完成
     *
     * @param inoutDetail 出入库记录
     */
    private void afterCommitDone(InoutDetail inoutDetail) {
        // 提交操作
        if (inoutDetail.getOptFlg() == 1) {
            // 更新任务完成数量
            BigDecimal totalQty = inoutDetail.getDetailList().stream().map(InoutItem::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            inoutTaskService.updateCompletedQty(inoutDetail.getTaskCode(), totalQty);

            // 更新库存、出入库日志
            inventoryLinker.onChange(inoutDetail);
        }
    }

    @Override
    public void generateOrderStatistics() {
        //首先，查询前一天的所有出入库类型为销售、生产订单的记录及其详情
        //然后，根据记录，生成销售、生产订单统计数据

        String date = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        LambdaQueryWrapper<InoutDetail> query = Wrappers.<InoutDetail>lambdaQuery()
                .in(InoutDetail::getInoutBizType, "0101", "0201")
                .eq(InoutDetail::getOptTime, date)
                .eq(InoutDetail::getOptFlg, 1)
                .eq(InoutDetail::getEnabled, 1);
        List<InoutDetail> inoutDetailList = this.list(query);
        //然后查询对应的详情
        inoutDetailList.forEach(detail -> detail.setDetailList(inoutItemService.getListByMainId(detail.getId())));
        //最后，根据出入库记录，生成销售、生产订单统计数据
        inventoryLinker.generateOrderStatistics(inoutDetailList);
    }
}

