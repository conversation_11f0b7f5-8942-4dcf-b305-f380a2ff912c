package com.hxdi.nmjl.trash.domain.quality;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 质检设备
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("BUSI_QUALITY_MACHINE")
public class BusiQualityMachine extends DataEntity<BusiQualityMachine> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("QUALITY_MECHANISM_ID")
    private Integer qualityMechanismId;

    @TableField("MACHINE_NAME")
    private String machineName;

    @TableField("SPEC")
    private String spec;

    @TableField("MANUFACTURER_COUNTRY")
    private String manufacturerCountry;

    @TableField("AMOUNT")
    private String amount;

    @TableField("BUY_YEAR")
    private String buyYear;

    @TableField("USAGE_RATE")
    private String usageRate;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;
}
