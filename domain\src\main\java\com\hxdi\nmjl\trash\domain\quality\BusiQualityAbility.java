package com.hxdi.nmjl.trash.domain.quality;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 质检能力
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("BUSI_QUALITY_ABILITY")
public class BusiQualityAbility extends DataEntity<BusiQualityAbility> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("QUALITY_MECHANISM_ID")
    private Integer qualityMechanismId;

    @TableField("FIELD")
    private String field;

    @TableField("PROJECT_NAME")
    private String projectName;

    @TableField("METHOD")
    private String method;

    @TableField("LIMIT_RANGE")
    private String limitRange;

    @TableField("ASSET_PROOF")
    private String assetProof;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;
}
