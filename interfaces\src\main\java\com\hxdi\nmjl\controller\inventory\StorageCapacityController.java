package com.hxdi.nmjl.controller.inventory;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.domain.inventory.StorageCapacity;
import com.hxdi.nmjl.condition.inventory.StorageCapacityCondition;
import com.hxdi.nmjl.service.inventory.StorageCapacityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 库容管理
 * <AUTHOR>
 * @version 1.0
 * @since 2025/07/19 20:02
 */
@Api(tags = "库容管理")
@RestController
@RequestMapping("/storageCapacity")
public class StorageCapacityController extends BaseController<StorageCapacityService, StorageCapacity> {

    @ApiOperation("分页查询")
    @GetMapping("/page")
    public ResultBody<Page<StorageCapacity>> getPages(StorageCapacityCondition condition) {
        return ResultBody.ok().data(bizService.getPages(condition));
    }

    @ApiOperation("列表查询")
    @GetMapping("/list")
    public ResultBody<List<StorageCapacity>> getList(StorageCapacityCondition condition) {
        return ResultBody.ok().data(bizService.getList(condition));
    }

    @ApiOperation("查看详情")
    @GetMapping("/getDetail")
    public ResultBody<StorageCapacity> getDetail(@RequestParam String id) {
        return ResultBody.ok().data(bizService.getDetail(id));
    }

    @ApiOperation("删除库容信息")
    @PostMapping("/delete")
    public ResultBody<Boolean> delete(@RequestParam String id) {
        return ResultBody.ok().data(bizService.delete(id));
    }

    @ApiOperation("保存/更新")
    @PostMapping("/saveOrUpdate")
    public ResultBody<Void> saveOrUpdate(@RequestBody StorageCapacity storageCapacity) {
        if (CommonUtils.isEmpty(storageCapacity.getId())) {
            bizService.add(storageCapacity);
        } else {
            bizService.update(storageCapacity);
        }
        return ResultBody.ok();
    }
}
