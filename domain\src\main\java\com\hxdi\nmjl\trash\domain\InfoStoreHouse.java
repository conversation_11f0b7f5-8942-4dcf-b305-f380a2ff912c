package com.hxdi.nmjl.trash.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 仓房
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("INFO_STORE_HOUSE")
public class InfoStoreHouse extends DataEntity<InfoStoreHouse> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("PID")
    private Integer pid;

    @TableField("STORE_ID")
    private Integer storeId;

    @TableField("NAME")
    private String name;

    @TableField("CODER")
    private String coder;

    @TableField("TYPER")
    private String typer;

    @TableField("CAPACITY")
    private BigDecimal capacity;

    @TableField("UNIT")
    private String unit;

    @TableField("YEAR")
    private String year;

    @TableField("BUILD_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date buildDate;

    @TableField("BUILD_TYPER")
    private String buildTyper;

    @TableField("HOUSE_HIGH")
    private BigDecimal houseHigh;

    @TableField("HOUSE_WIDE")
    private BigDecimal houseWide;

    @TableField("HOUSE_LONG")
    private BigDecimal houseLong;

    @TableField("HOUSE_STATUS")
    private String houseStatus;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;
}
