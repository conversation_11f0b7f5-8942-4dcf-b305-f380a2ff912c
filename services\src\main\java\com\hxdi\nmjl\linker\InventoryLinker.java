package com.hxdi.nmjl.linker;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hxdi.common.core.utils.CachedBeanCopyUtils;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.cache.CacheProvider;
import com.hxdi.nmjl.domain.base.Catalog;
import com.hxdi.nmjl.domain.base.Organization;
import com.hxdi.nmjl.domain.base.StoreHouse;
import com.hxdi.nmjl.domain.base.StoreLocation;
import com.hxdi.nmjl.domain.inout.DispatchInfo;
import com.hxdi.nmjl.domain.inout.DispatchTask;
import com.hxdi.nmjl.domain.inout.InoutDetail;
import com.hxdi.nmjl.domain.inout.InoutTask;
import com.hxdi.nmjl.domain.inventory.*;
import com.hxdi.nmjl.dto.inventory.InventoryDTO;
import com.hxdi.nmjl.enums.InoutBusinessType;
import com.hxdi.nmjl.enums.LocState;
import com.hxdi.nmjl.enums.TaskStatus;
import com.hxdi.nmjl.service.inout.DispatchInfoService;
import com.hxdi.nmjl.service.inout.DispatchTaskService;
import com.hxdi.nmjl.service.inout.InoutTaskService;
import com.hxdi.nmjl.service.inventory.InventoryLogService;
import com.hxdi.nmjl.service.inventory.InventoryService;
import com.hxdi.nmjl.service.inventory.LocationCardService;
import com.hxdi.nmjl.service.inventory.TraceInfoService;
import com.hxdi.nmjl.service.plan.ProductionOrderService;
import com.hxdi.nmjl.service.plan.SaleOrderService;
import com.hxdi.nmjl.service.base.OrganizationService;
import com.hxdi.nmjl.utils.RedisKeys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 库存连接器组件：用于解耦库存服务与其他服务之间的业务逻辑，连接器只是处理对象封装，不涉及具体业务逻辑
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/15 00:02
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class InventoryLinker {

    @Autowired
    private ContextService contextService;

    @Autowired
    private InventoryService inventoryService;

    @Resource
    private InventoryLogService inventoryLogService;

    @Resource
    private LocationCardService locationCardService;

    @Resource
    private TraceInfoService traceInfoService;

    @Resource
    private DispatchTaskService dispatchTaskService;

    @Resource
    private DispatchInfoService dispatchInfoService;

    @Resource
    private InoutTaskService inoutTaskService;

    @Resource
    private SaleOrderService salesOrderService;

    @Resource
    private ProductionOrderService productionOrderService;

    @Resource
    private OrganizationService organizationService;


    /**
     * 监听出入库事件，更新库存、库存日志、货位卡
     *
     * @param inoutDetail
     */
    public void onChange(InoutDetail inoutDetail) {
        List<InventoryDTO> inventoryDTOList = new ArrayList<>();
        inoutDetail.getDetailList().forEach(inoutItem -> {
            InventoryDTO inventoryDTO = new InventoryDTO();
            inventoryDTO.setStoreId(inoutDetail.getStoreId());
            inventoryDTO.setStId(inoutItem.getStId());
            inventoryDTO.setLocId(inoutItem.getLocId());
            inventoryDTO.setCatalogId(inoutItem.getCatalogId());
            inventoryDTO.setSpecifications(inoutItem.getSpecification());
            inventoryDTO.setGrade(inoutItem.getGrade());
            inventoryDTO.setReserveLevel(inoutDetail.getReserveLevel());
            inventoryDTO.setProductionDate(inoutItem.getProductDate());
            inventoryDTO.setManageUnitId(inoutDetail.getOrgId());
            inventoryDTO.setChangeQty(inoutItem.getQty());
            inventoryDTO.setInoutType(inoutDetail.getInoutType());
            inventoryDTO.setBizType(inoutDetail.getInoutBizType());
            inventoryDTO.setMainBizId(inoutDetail.getId());
            inventoryDTO.setSubBizId(inoutItem.getId());
            inventoryDTO.setBatchNum(inoutDetail.getBatchNum());

            inventoryDTOList.add(inventoryDTO);
        });

        // 1. 更新库存
        inventoryService.updateBatch(inventoryDTOList);

        // 2. 添加库存更新日志
        addInventoryLog(inventoryDTOList);

        // 3. 更新货位卡
        updateLocationCard(inventoryDTOList);

        // 4. 添加库存追溯信息
        updateTraceInfo(inventoryDTOList);
    }

    /**
     * 监听库存调整单事件
     *
     * @param inventoryAdjustment
     */
    public void onChange(InventoryAdjustment inventoryAdjustment) {
        List<InventoryDTO> inventoryDTOList = new ArrayList<>();
        inventoryAdjustment.getDetailList().forEach(item -> {
            InventoryBase inventoryBase = item.getInventoryBase();
            InventoryDTO inventoryDTO = new InventoryDTO();
            inventoryDTO.setStoreId(inventoryAdjustment.getStoreId());
            inventoryDTO.setStId(inventoryBase.getStId());
            inventoryDTO.setLocId(inventoryBase.getLocId());
            inventoryDTO.setCatalogId(inventoryBase.getCatalogId());
            inventoryDTO.setSpecifications(inventoryBase.getSpecifications());
            inventoryDTO.setGrade(inventoryBase.getGrade());
            inventoryDTO.setReserveLevel(inventoryBase.getReserveLevel());
            inventoryDTO.setProductionDate(inventoryBase.getProductionDate());
            inventoryDTO.setManageUnitId(inventoryBase.getManageUnitId());

            if (item.getAdjustQty().compareTo(BigDecimal.ZERO) < 0) {
                inventoryDTO.setInoutType(InoutBusinessType.OUT.getCode());
                inventoryDTO.setBizType(InoutBusinessType.OUT_ADJUSTMENT.getCode());
                inventoryDTO.setChangeQty(item.getAdjustQty().negate());
            } else {
                inventoryDTO.setInoutType(InoutBusinessType.IN.getCode());
                inventoryDTO.setBizType(InoutBusinessType.IN_ADJUSTMENT.getCode());
                inventoryDTO.setChangeQty(item.getAdjustQty());
            }

            inventoryDTO.setMainBizId(inventoryAdjustment.getId());
            inventoryDTO.setSubBizId(item.getId());

            inventoryDTOList.add(inventoryDTO);
        });

        // 1. 更新库存
        inventoryService.updateBatch(inventoryDTOList);

        // 2. 添加库存更新日志
        addInventoryLog(inventoryDTOList);
    }

    /**
     * 监听库存拆分事件
     *
     * @param dispatchInfo
     */
    public void onChange(List<DispatchInfo> dispatchInfo) {
        List<InventoryDTO> inventoryDTOList = new ArrayList<>();
        //总出库库存数量(用于更新被拆分的库存记录)
        AtomicReference<BigDecimal> totalQty = new AtomicReference<>(BigDecimal.ZERO);
        DispatchTask task = dispatchTaskService.getById(dispatchInfo.get(0).getTaskId());
        //根据拆分计划获取需要被拆分的库存记录
        Inventory inventoryOut = inventoryService.getById(task.getInventoryId());
        //将当前已拆分出库的库存数量加到总出库库存数量中
//        totalQty.updateAndGet(v -> v.add(task.getCompletedQty()));

        //构造需要入库的库存记录（拆分入库）
        dispatchInfo.forEach(item -> {
            InventoryDTO inventoryDTO = new InventoryDTO();

            inventoryDTO.setStoreId(item.getStoreId());
            inventoryDTO.setStId(item.getStId());
            inventoryDTO.setLocId(item.getLocId());
            inventoryDTO.setCatalogId(inventoryOut.getCatalogId());
            inventoryDTO.setSpecifications(inventoryOut.getSpecifications());
            inventoryDTO.setGrade(inventoryOut.getGrade());
            inventoryDTO.setReserveLevel(inventoryOut.getReserveLevel());
            inventoryDTO.setProductionDate(inventoryOut.getProductionDate());
            inventoryDTO.setManageUnitId(inventoryOut.getManageUnitId());
            inventoryDTO.setInoutType(InoutBusinessType.IN.getCode());
            inventoryDTO.setBizType(InoutBusinessType.IN_DISPATCH.getCode());
            inventoryDTO.setChangeQty(item.getQty());
            // 更新被拆分的库存总数量
            totalQty.updateAndGet(v -> v.add(item.getQty()));

            inventoryDTO.setMainBizId(item.getId());

            inventoryDTOList.add(inventoryDTO);

            //更新拆分记录状态
            item.setStatus(2);
        });

        //构造被拆分的库存数据
        if (totalQty.get().compareTo(BigDecimal.ZERO) > 0) {
            InventoryDTO inventoryDTO = new InventoryDTO();
            inventoryDTO.setStoreId(inventoryOut.getStoreId());
            inventoryDTO.setStId(inventoryOut.getStId());
            inventoryDTO.setLocId(inventoryOut.getLocId());
            inventoryDTO.setCatalogId(inventoryOut.getCatalogId());
            inventoryDTO.setSpecifications(inventoryOut.getSpecifications());
            inventoryDTO.setGrade(inventoryOut.getGrade());
            inventoryDTO.setReserveLevel(inventoryOut.getReserveLevel());
            inventoryDTO.setProductionDate(inventoryOut.getProductionDate());
            inventoryDTO.setManageUnitId(inventoryOut.getManageUnitId());
            inventoryDTO.setInoutType(InoutBusinessType.OUT.getCode());
            inventoryDTO.setBizType(InoutBusinessType.OUT_DISPATCH.getCode());
            //将被拆分的库存总数量更新到库存记录中
            inventoryDTO.setChangeQty(totalQty.get());
            inventoryDTO.setMainBizId(task.getId());

            inventoryDTOList.add(inventoryDTO);

            // 更新库存拆分计划状态及计划完成数量
            if (task.getPlanQty().equals(totalQty.updateAndGet(v -> v.add(task.getCompletedQty())))) {
                task.setState(TaskStatus.COMPLETED.getCode());
            } else {
                task.setState(TaskStatus.EXECUTING.getCode());
            }
            task.setCompletedQty(totalQty.get());
        }

        // 1. 更新库存
        inventoryService.updateBatch(inventoryDTOList);

        // 2. 添加库存更新日志
        addInventoryLog(inventoryDTOList);

        // 3. 更新货位卡
        updateLocationCard(inventoryDTOList);

        // 4. 添加库存追溯信息
        updateTraceInfo(inventoryDTOList);

        // 5. 更新库存拆分调度计划
        dispatchTaskService.updateById(task);

        dispatchInfoService.updateBatchById(dispatchInfo);
    }


    /**
     * 保存或更新货位卡
     *
     * @param dtoList
     */
    private void updateLocationCard(List<InventoryDTO> dtoList) {
        List<LocationCard> locationCardList = new ArrayList<>();
        Map<String, LocationCard> distinctFilter = new HashMap<>();
        dtoList.forEach(data -> {
            LocationCard locationCard = distinctFilter.get(data.getLocId());

            if (locationCard == null) {
                locationCard = locationCardService.getDetailByLocId(data.getLocId());
            }

            Integer currentState = 9;
            if (locationCard == null) {
                locationCard = new LocationCard();
                CachedBeanCopyUtils.copy(data, locationCard);
                locationCard.setStName(CacheProvider.getValue(RedisKeys.STORE_HOUSE.key(), data.getStId(), StoreHouse::getName));
                locationCard.setLocName(CacheProvider.getValue(RedisKeys.STORE_LOCATION.key(), data.getLocId(), StoreLocation::getName));
                locationCard.setStoreName(CacheProvider.getValue(RedisKeys.ORGANIZATION.key(), data.getStoreId(), Organization::getOrgName));
                distinctFilter.put(data.getLocId(), locationCard);
            } else {
                currentState = locationCard.getLocState();
            }

            if (data.isInType()) {
                locationCard.setLocState(LocState.USING.getCode());
            } else if (!data.isInType() && data.getCurrentInventoryQty().compareTo(data.getChangeQty()) == 0) {
                locationCard.setLocState(LocState.EMPTY.getCode());
            }

            if (locationCard.getLocState().equals(currentState)) {
                return;
            }

            locationCardList.add(locationCard);
        });

        //保存货位卡
        locationCardService.saveOrUpdateBatch(locationCardList);
    }


    /**
     * 更新库存追溯信息
     *
     * @param dtoList
     */
    private void updateTraceInfo(List<InventoryDTO> dtoList) {
        List<TraceInfo> traceList = new ArrayList<>();
        dtoList.forEach(data -> {
            if (data.isInType() && CommonUtils.isNotEmpty(data.getBatchNum())) {
                boolean isExists = traceInfoService.exists(data.getInventoryId(), data.getBatchNum());
                if (!isExists) {
                    TraceInfo traceInfo = new TraceInfo();
                    traceInfo.setBatchNum(data.getBatchNum());
                    traceInfo.setInventoryId(data.getInventoryId());
                    traceList.add(traceInfo);
                }
            }
        });

        traceInfoService.saveBatch(traceList);
    }

    /**
     * 添加库存更新日志
     *
     * @param dtoList
     */
    private void addInventoryLog(List<InventoryDTO> dtoList) {
        List<InventoryLog> inventoryLogList = new ArrayList<>();
        dtoList.forEach(data -> {
            InventoryLog inventoryLog = new InventoryLog();
            CachedBeanCopyUtils.copy(data, inventoryLog);
            inventoryLog.setCatalogName(CacheProvider.getValue(RedisKeys.CATALOG.key(), data.getCatalogId(), Catalog::getCatalogName));
            inventoryLog.setBeforeInventoryQty(data.getCurrentInventoryQty());
            Optional<Organization> store = CacheProvider.optional(RedisKeys.ORGANIZATION.key(), data.getStoreId());
            inventoryLog.setProvince(CommonUtils.getOptionalValue(store, Organization::getProvince));
            inventoryLog.setCity(CommonUtils.getOptionalValue(store, Organization::getCity));
            inventoryLog.setCounty(CommonUtils.getOptionalValue(store, Organization::getCounty));
            inventoryLog.setPrice(contextService.findPrice(data.getMainBizId(), data.getBizType(), data.getCatalogId(), data.getGrade()));
            inventoryLogList.add(inventoryLog);
        });

        //保存库存更新日志
        inventoryLogService.saveBatch(inventoryLogList);
    }


    /**
     * 根据传入的出入库记录，获取对应的生产销售订单
     */
    public void generateOrderStatistics(List<InoutDetail> inoutDetailList) {
        if (inoutDetailList == null || inoutDetailList.isEmpty()) {
            log.warn("出入库明细数据为空，跳过统计生成");
            return;
        }

        //查询出入库记录对应的出入库任务单
        List<String> taskCode= inoutDetailList.stream().map(InoutDetail::getTaskCode).distinct().collect(Collectors.toList());
        LambdaQueryWrapper<InoutTask> query = Wrappers.<InoutTask>lambdaQuery().in(InoutTask::getTaskCode, taskCode);
        List<InoutTask> inoutTaskList = inoutTaskService.list(query);

        //为所有的出入库记录，添加对应的订单编号
        inoutDetailList.forEach(detail -> {
            Optional<InoutTask> task = inoutTaskList.stream().filter(t -> t.getTaskCode().equals(detail.getTaskCode())).findFirst();
            detail.setOrderCode(task.map(InoutTask::getOrderId).orElse(null));
        });

        // 获取所有库点ID，批量查询组织信息以获取areaCode
        List<String> storeIds = inoutDetailList.stream()
                .map(InoutDetail::getStoreId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        Map<String, String> storeAreaCodeMap = new HashMap<>();
        if (!storeIds.isEmpty()) {
            List<Organization> organizations = organizationService.listByIds(storeIds);
            storeAreaCodeMap = organizations.stream()
                    .collect(Collectors.toMap(Organization::getId,
                            org -> org.getCounty() != null ? org.getCounty() : "",
                            (existing, replacement) -> existing));
        }

        // 为出入库记录设置areaCode
        final Map<String, String> finalStoreAreaCodeMap = storeAreaCodeMap;
        inoutDetailList.forEach(detail -> {
            if (detail.getStoreId() != null) {
                String areaCode = finalStoreAreaCodeMap.get(detail.getStoreId());
                // 将areaCode存储在扩展字段中，供后续统计使用
                if (areaCode != null && !areaCode.trim().isEmpty()) {
                    detail.setAreaCode(areaCode);
                }
            }
        });

        //根据业务类型分组，分别处理销售订单和生产订单
        Map<String, List<InoutDetail>> bizTypeMap = inoutDetailList.stream().collect(Collectors.groupingBy(InoutDetail::getInoutBizType));
        bizTypeMap.forEach((bizType, detailList) -> {
            if (InoutBusinessType.OUT_SALE.getCode().equals(bizType)) {
                //销售订单
                salesOrderService.generateOrderStatistics(detailList);
            } else if (InoutBusinessType.IN_PURCHASE.getCode().equals(bizType)) {
                //生产订单
                productionOrderService.generateOrderStatistics(detailList);
            } else {
                log.warn("未识别的业务类型: {}, 跳过统计处理", bizType);
            }
        });
    }

}
