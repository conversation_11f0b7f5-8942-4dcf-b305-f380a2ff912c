package com.hxdi.nmjl.controller.inventory;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.nmjl.condition.inventory.InventoryAllocationCondition;
import com.hxdi.nmjl.domain.inventory.InventoryAllocation;
import com.hxdi.nmjl.service.inventory.InventoryAllocationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@Api(tags = "库存调度计划")
@RequestMapping("inventoryAllocation")
public class InventoryAllocationController extends BaseController<InventoryAllocationService, InventoryAllocation> {


    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("/get")
    @ApiOperation(value = "单条查询")
    public ResultBody<InventoryAllocation> selectOne(String id) {
        return ResultBody.ok().data(bizService.getById(id));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("/getMore")
    @ApiOperation(value = "单条查询（包含调配详情）")
    public ResultBody<InventoryAllocation> selectOneV1(String id) {
        return ResultBody.ok().data(bizService.getMore(id));
    }


    @PostMapping("/createOrUpdate")
    @ApiOperation(value = "创建或更新拆分调度计划（注意：无法修改已经开始执行的计划）")
    public ResultBody<InventoryAllocation> createOrUpdate(@RequestBody InventoryAllocation InventoryAllocation) {
        bizService.createOrUpdate(InventoryAllocation);
        return ResultBody.ok().data(InventoryAllocation);
    }

    @GetMapping("/remove")
    @ApiOperation(value = "删除调度计划")
    public ResultBody remove(@RequestParam("id") String id) {
        bizService.removeById(id);
        return ResultBody.ok();
    }

    @GetMapping("/list")
    @ApiOperation(value = "根据条件查询计划列表（带权限控制）")
    public ResultBody<List<InventoryAllocation>> list(InventoryAllocationCondition condition) {
        return ResultBody.ok().data(bizService.listV1(condition));
    }

    @GetMapping("/page")
    @ApiOperation(value = "根据条件查询计划分页列表（带权限控制）")
    public ResultBody<Page<InventoryAllocation>> page(InventoryAllocationCondition condition) {
        return ResultBody.ok().data(bizService.PageV1(condition));
    }

    @ApiOperation("审批调配订单")
    @GetMapping("/submit")
    public ResultBody<Void> submit(@RequestParam String id) {
        bizService.submitV1(id);
        return ResultBody.ok();
    }

}
