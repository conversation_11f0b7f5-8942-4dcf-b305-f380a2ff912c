package com.hxdi.nmjl.mapper.emergency;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.condition.emergency.EmergencyEventCondition;
import com.hxdi.nmjl.domain.emergency.EmergencyEvent;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 应急事件 Mapper 接口
 */
public interface EmergencyEventMapper extends SuperMapper<EmergencyEvent> {

    /**
     * 列表查询应急事件信息
     *
     * @param condition 查询条件
     * @return List<EmergencyEvent>
     */
    @DataPermission
    List<EmergencyEvent> getList(@Param("condition") EmergencyEventCondition condition);

    /**
     * 分页查询应急事件信息
     *
     * @param condition 查询条件
     * @param page      分页对象
     * @return Page<EmergencyEvent>
     */
    @DataPermission
    Page<EmergencyEvent> getPages(@Param("condition") EmergencyEventCondition condition, @Param("page") Page<EmergencyEvent> page);
}
