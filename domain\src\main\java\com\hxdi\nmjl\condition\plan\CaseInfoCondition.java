package com.hxdi.nmjl.condition.plan;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@ApiModel(description = "案件查询参数")
@Setter
@Getter
public class CaseInfoCondition extends QueryCondition {
    @ApiModelProperty(value = "发生时间：开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime1;

    @ApiModelProperty(value = "发生时间：结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime1;

    @ApiModelProperty(value = "立案时间：开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime2;

    @ApiModelProperty(value = "立案时间：结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime2;

    @ApiModelProperty(value = "结案时间：开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime3;

    @ApiModelProperty(value = "结案时间：结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime3;

    @ApiModelProperty(value = "业务状态：0-未执行，1-执行中，2-已立案，3-已结案", example = "1")
    private Integer state;
}
