package com.hxdi.nmjl.enums;

import lombok.Getter;

/**
 * 供应商评价权重
 */
@Getter
public enum SupplierEvaluationDimension {
    CREDIT("信用评分", 0.3),     // 信用权重30%
    TIME("准时性评分", 0.2),    // 准时性权重20%
    PRICE("价格评分", 0.2),     // 价格权重20%
    SERVICE("服务评分", 0.3);   // 服务权重30%

    private final String name;  // 维度名称
    private final double weight; // 对应权重

    SupplierEvaluationDimension(String name, double weight) {
        this.name = name;
        this.weight = weight;
    }
    public String getName() {
        return name;
    }
    public double getWeight() {
        return weight;
    }

}
