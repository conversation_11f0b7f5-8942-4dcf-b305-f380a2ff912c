package com.hxdi.nmjl.trash.domain.mobilize;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 动员企业能力
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("BUSI_MOBILIZE_ENTERPRISE_ABILITY")
public class BusiMobilizeEnterpriseAbility extends DataEntity<BusiMobilizeEnterpriseAbility> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("ENTERPRISE_ID")
    private Integer enterpriseId;

    @TableField("ROOT_FOOD_CATEGORY_ID")
    private String rootFoodCategoryId;

    @TableField("ROOT_FOOD_CATEGORY_NAME")
    private String rootFoodCategoryName;

    @TableField("FOOD_CATEGORY_ID")
    private String foodCategoryId;

    @TableField("FOOD_CATEGORY_CODE")
    private String foodCategoryCode;

    @TableField("FOOD_CATEGORY_NAME")
    private String foodCategoryName;

    @TableField("SPEC")
    private String spec;

    @TableField("UNIT")
    private String unit;

    @TableField("NORMAL_QUANTITY")
    private BigDecimal normalQuantity;

    @TableField("MAX_QUANTITY")
    private BigDecimal maxQuantity;


    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;
}
