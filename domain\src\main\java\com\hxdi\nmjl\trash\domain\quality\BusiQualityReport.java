package com.hxdi.nmjl.trash.domain.quality;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 质检报告
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("BUSI_QUALITY_REPORT")
public class BusiQualityReport extends DataEntity<BusiQualityReport> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("CONTRACT_ID")
    private Integer contractId;

    @TableField("CONTRACT_CODE")
    private String contractCode;

    @TableField("PRODUCTION_BATCH")
    private String productionBatch;

    @TableField("CODE")
    private String code;

    @TableField("ROOT_FOOD_CATEGORY_ID")
    private String rootFoodCategoryId;

    @TableField("ROOT_FOOD_CATEGORY_NAME")
    private String rootFoodCategoryName;

    @TableField("FOOD_CATEGORY_ID")
    private String foodCategoryId;

    @TableField("FOOD_CATEGORY_CODE")
    private String foodCategoryCode;

    @TableField("FOOD_CATEGORY_NAME")
    private String foodCategoryName;

    @TableField("ENTERPRISE_ID")
    private Integer enterpriseId;

    @TableField("ENTERPRISE_CODE")
    private String enterpriseCode;

    @TableField("ENTERPRISE_NAME")
    private String enterpriseName;

    @TableField("ARRIVAL_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date arrivalDate;

    @TableField("PRODUCT_NAME")
    private String productName;

    @TableField("MANUFACTURER")
    private String manufacturer;

    @TableField("BATCH")
    private String batch;

    @TableField("PRODUCTION_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date productionDate;

    @TableField("PRODUCT_BATCH")
    private String productBatch;

    @TableField("SAMPLE_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date sampleDate;

    @TableField("SAMPLE_MAN")
    private String sampleMan;

    @TableField("SAMPLE_CODE")
    private String sampleCode;

    @TableField("SAMPLE_ADDRESS")
    private String sampleAddress;

    @TableField("SAMPLE_DESC")
    private String sampleDesc;

    @TableField("ORIGIN")
    private String origin;

    @TableField("SHELF_LIFE")
    private String shelfLife;

    @TableField("HARVEST_YEAR")
    private String harvestYear;

    @TableField("TYPER")
    private String typer;

    @TableField("IS_QUALIFIED")
    private String isQualified;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;


    @TableField("TEST_CODE")
    private String testCode;
    @TableField("TEST_TYPE")
    private String testType;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField("TEST_DATE")
    private Date testDate;
    @TableField("TEST_NAME")
    private String testName;
    @TableField("TEST_RESULT")
    private String testResult;

    @TableField("QUALITY_CODE")
    private String qualityCode;

    @TableField("QUALITY_NAME")
    private String qualityName;

    @TableField("QUALITY_TIME")
    private Date qualityTime;

    @TableField("QUALITY_STATUS")
    private String qualityStatus;

    @TableField("QUALITY_RESULT")
    private String qualityResult;
}
