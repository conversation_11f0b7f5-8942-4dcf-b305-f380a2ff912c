package com.hxdi.nmjl.trash.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 资产
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("INFO_ASSETS")
public class InfoAssets extends DataEntity<InfoAssets> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("ASSETS_NAME")
    private String assetsName;

    @TableField("AMOUNT")
    private BigDecimal amount;

    @TableField("QUANTITY")
    private BigDecimal quantity;

    @TableField("OFFICE_ID")
    private Integer officeId;

    @TableField("OFFICE_NAME")
    private String officeName;

    @TableField("USED_BY")
    private String usedBy;

    @TableField("GET_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date getDate;

    @TableField("USE_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date useDate;

    @TableField("BRAND_NAME")
    private String brandName;

    @TableField("SPEC")
    private String spec;

    @TableField("STORAGE_LOC")
    private String storageLoc;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;
}
