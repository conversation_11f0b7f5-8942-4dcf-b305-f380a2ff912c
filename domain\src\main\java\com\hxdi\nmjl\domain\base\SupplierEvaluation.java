package com.hxdi.nmjl.domain.base;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 供应商评价信息
 */
@ApiModel(description = "供应商评价信息")
@Getter
@Setter
@TableName("B_SUPPLIER_EVALUATION") // 表名映射
public class SupplierEvaluation implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    @TableField("SUPPLIER_ID")
    @ApiModelProperty(value = "供应商ID")
    private String supplierId;

    @TableField("SUPPLIER_NAME")
    @ApiModelProperty(value = "供应商名字")
    private String supplierName;

    @TableField("COMMENTS")
    @ApiModelProperty(value = "评价内容")
    private String comments;

    @TableField("SERV_SCORE")
    @ApiModelProperty(value = "服务评分: 1-5分", example = "5")
    private Integer servScore;

    @TableField("PRICE_SCORE")
    @ApiModelProperty(value = "价格评分: 1-5分", example = "5")
    private Integer priceScore;

    @TableField("TIME_SCORE")
    @ApiModelProperty(value = "准时性评分: 1-5分", example = "5")
    private Integer timeScore;

    @TableField("CREDIT_SCORE")
    @ApiModelProperty(value = "信用评分: 1-5分", example = "5")
    private Integer creditScore;

    @TableField("EVALUATOR")
    @ApiModelProperty(value = "评价用户")
    private String evaluator;

    @TableField("ENABLED")
    @ApiModelProperty(value = "状态（1-正常 0-删除）", example = "1")
    private Integer enabled;

    @TableField("ATTACHMENTS")
    @ApiModelProperty(value = "附件")
    private String attachments;

    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @TableField("CREATE_ID")
    @ApiModelProperty(value = "创建id")
    private String createId;

    @TableField("UPDATE_ID")
    @ApiModelProperty(value = "更新id")
    private String updateId;

    @TableField("TENANT_ID")
    @ApiModelProperty(value = "租户id")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;
}
