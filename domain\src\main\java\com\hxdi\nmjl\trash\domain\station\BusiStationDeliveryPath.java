package com.hxdi.nmjl.trash.domain.station;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 配送路径
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("BUSI_STATION_DELIVERY_PATH")
public class BusiStationDeliveryPath extends DataEntity<BusiStationDeliveryPath> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("DELIVERY_ID")
    private Integer deliveryId;

    @TableField("LONGITUDE")
    private BigDecimal longitude;

    @TableField("LATITUDE")
    private BigDecimal latitude;

    @TableField("ADDRESS")
    private String address;

    @TableField("RECORD_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date recordTime;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;
}
