package com.hxdi.nmjl.service.inventory.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hxdi.nmjl.domain.inventory.InventoryAllocationDetail;
import com.hxdi.nmjl.service.inventory.InventoryAllocationDetailService;
import com.hxdi.nmjl.mapper.inventory.InventoryAllocationDetailMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【B_INVENTORY_ALLOCATION_DETAIL(库存调配明细)】的数据库操作Service实现
* @createDate 2025-08-01 10:28:22
*/
@Service
public class InventoryAllocationDetailServiceImpl extends ServiceImpl<InventoryAllocationDetailMapper, InventoryAllocationDetail>
    implements InventoryAllocationDetailService {

}




