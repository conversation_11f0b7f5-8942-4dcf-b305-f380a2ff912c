package com.hxdi.nmjl.service.inventory.impl;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hxdi.nmjl.domain.inventory.InventoryAllocationDetail;
import com.hxdi.nmjl.domain.plan.SaleOrderItem;
import com.hxdi.nmjl.mapper.inventory.InventoryAllocationDetailMapper;
import com.hxdi.nmjl.service.inventory.InventoryAllocationDetailService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【B_INVENTORY_ALLOCATION_DETAIL(库存调配明细)】的数据库操作Service实现
* @createDate 2025-08-01 10:28:22
*/
@Service
public class InventoryAllocationDetailServiceImpl extends ServiceImpl<InventoryAllocationDetailMapper, InventoryAllocationDetail>
    implements InventoryAllocationDetailService {

    @Override
    public void removeV1(String id) {
        baseMapper.delete(Wrappers.<InventoryAllocationDetail>lambdaQuery().eq(InventoryAllocationDetail::getAllocateId, id));
    }

    @Override
    public List<InventoryAllocationDetail> getListV1(String id) {
        return baseMapper.selectList(Wrappers.<InventoryAllocationDetail>lambdaQuery().eq(InventoryAllocationDetail::getAllocateId, id));
    }


}




