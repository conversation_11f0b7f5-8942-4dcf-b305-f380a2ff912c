package com.hxdi.nmjl.service.portal.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.portal.PortalCondition;
import com.hxdi.nmjl.condition.portal.ResourceCondition;
import com.hxdi.nmjl.domain.portal.CmsCategories;
import com.hxdi.nmjl.domain.portal.CmsData;
import com.hxdi.nmjl.domain.portal.CmsResources;
import com.hxdi.nmjl.mapper.portal.CmsResourcesMapper;
import com.hxdi.nmjl.service.portal.CmsCategoriesService;
import com.hxdi.nmjl.service.portal.CmsDataService;
import com.hxdi.nmjl.service.portal.CmsResourcesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;


/**
 * 门户资源服务实现
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/18 16:55
 */
@Transactional(rollbackFor = Exception.class)
@Service
@Slf4j
public class CmsResourcesServiceImpl extends BaseServiceImpl<CmsResourcesMapper, CmsResources> implements CmsResourcesService {

    @Autowired
    private CmsCategoriesService cmsCategoriesService;

    @Autowired
    private CmsDataService cmsDataService;

    @Override
    public void removeByCategoryId(String categoryId) {
        baseMapper.update(null, Wrappers.<CmsResources>lambdaUpdate().set(CmsResources::getEnabled, 0).eq(CmsResources::getCategoryId, categoryId));
    }

    @Override
    public void create(CmsResources resources) {
        if (CommonUtils.isNotEmpty(resources.getContentData())) {
            CmsData contentData = new CmsData();
            contentData.setId(IdWorker.getIdStr(new Object()));
            contentData.setDat(resources.getContentData());
            cmsDataService.save(contentData);

            resources.setContent(contentData.getId());
        }

        if (CommonUtils.isNotEmpty(resources.getImgData())) {
            CmsData imgData = new CmsData();
            imgData.setId(IdWorker.getIdStr(new Object()));
            imgData.setDat(resources.getImgData());
            cmsDataService.save(imgData);

            resources.setImg(imgData.getId());
        }

        CmsCategories category = cmsCategoriesService.getDetail(resources.getCategoryId());
        resources.setCategoryPath(category.getCategoryPath());
        resources.setModule(category.getModule());

        resources.setPublishState(0);
        baseMapper.insert(resources);
    }

    @Override
    public void update(CmsResources resources) {
        if (CommonUtils.isNotEmpty(resources.getContentData())) {
            CmsData contentData = new CmsData();
            contentData.setDat(resources.getContentData());
            if (CommonUtils.isEmpty(resources.getContent())) {
                contentData.setId(IdWorker.getIdStr(new Object()));
                cmsDataService.save(contentData);
            } else {
                contentData.setId(resources.getContent());
                cmsDataService.updateById(contentData);
            }
        }

        if (CommonUtils.isNotEmpty(resources.getImgData())) {
            CmsData imgData = new CmsData();
            imgData.setDat(resources.getImgData());
            if (CommonUtils.isEmpty(resources.getImg())) {
                imgData.setId(IdWorker.getIdStr(new Object()));
                cmsDataService.save(imgData);
            } else {
                imgData.setId(resources.getImg());
                cmsDataService.updateById(imgData);
            }
        }

        CmsCategories category = cmsCategoriesService.getDetail(resources.getCategoryId());
        resources.setCategoryPath(category.getCategoryPath());
        resources.setModule(category.getModule());

        baseMapper.updateById(resources);
    }

    @Override
    public void changeState(String resourceId, Integer state) {
        CmsResources resources = baseMapper.selectById(resourceId);
        if (resources == null) {
            BizExp.pop("资源不存在");
        }

        CmsResources updatingResource = new CmsResources();
        updatingResource.setPublishState(state);
        updatingResource.setId(resourceId);

        if (state == 1) {
            updatingResource.setPublishTime(new Date());
        }

        baseMapper.updateById(updatingResource);
    }

    @Override
    public void remove(String id) {
        CmsResources resources = new CmsResources();
        resources.setId(id);
        resources.setEnabled(0);
        baseMapper.updateById(resources);
    }

    @Override
    public CmsResources getDetail(String resourceId) {
        CmsResources resources = baseMapper.selectById(resourceId);

        if (CommonUtils.isNotEmpty(resources.getContent())) {
            CmsData data = cmsDataService.getData(resources.getContent());
            resources.setContentData(data.getDat());
        }

        if (CommonUtils.isNotEmpty(resources.getImg())) {
            CmsData data = cmsDataService.getData(resources.getImg());
            resources.setImgData(data.getDat());
        }

        resources.setCmsCategories(cmsCategoriesService.getDetail(resources.getCategoryId()));

        return resources;
    }

    @Override
    public Page<CmsResources> pages(ResourceCondition condition) {
        if (CommonUtils.isNotEmpty(condition.getCategoryId())) {
            condition.setCategoryPath(cmsCategoriesService.getBaseDetail(condition.getCategoryId()).getCategoryPath());
        }
        Page<CmsResources> page = condition.newPage();
        baseMapper.selectPageV1(page, condition);

        page.getRecords().forEach(item -> {
            item.setCmsCategories(cmsCategoriesService.getBaseDetail(item.getCategoryId()));
        });

        return page;
    }

    @Override
    public Page<CmsResources> pageForPortal(PortalCondition condition) {
        Page<CmsResources> page = condition.newPage();
        baseMapper.selectPageForPortal(page, condition);

        page.getRecords().forEach(item -> {
            item.setCmsCategories(cmsCategoriesService.getBaseDetail(item.getCategoryId()));

            if (CommonUtils.isNotEmpty(item.getImg())) {
                CmsData data = cmsDataService.getData(item.getImg());
                item.setImgData(data.getDat());
            }
        });
        return page;
    }
}
