package com.hxdi.nmjl.trash.domain.mobilize;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 动员协议
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("BUSI_MOBILIZE_AGREEMENT")
public class BusiMobilizeAgreement extends DataEntity<BusiMobilizeAgreement> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("CODE")
    private String code;

    @TableField("CATEGORY")
    private String category;

    @TableField("CONTRACT_TITLE")
    private String contractTitle;

    @TableField("CUSTOMER_ID")
    private Integer customerId;

    @TableField("CUSTOMER_CODE")
    private String customerCode;

    @TableField("CUSTOMER_NAME")
    private String customerName;

    @TableField("AMOUNT")
    private BigDecimal amount;

    @TableField("QUANTITY")
    private Integer quantity;

    @TableField("UNIT")
    private String unit;

    @TableField("CONTRACT_SIGNATORY")
    private String contractSignatory;

    @TableField("PRODUCT_ID")
    private Integer productId;

    @TableField("PRODUCT_CODE")
    private String productCode;

    @TableField("ROOT_FOOD_CATEGORY_ID")
    private String rootFoodCategoryId;

    @TableField("ROOT_FOOD_CATEGORY_NAME")
    private String rootFoodCategoryName;

    @TableField("FOOD_CATEGORY_ID")
    private String foodCategoryId;

    @TableField("FOOD_CATEGORY_CODE")
    private String foodCategoryCode;

    @TableField("FOOD_CATEGORY_NAME")
    private String foodCategoryName;

    @TableField("TOTAL_AMOUNT")
    private BigDecimal totalAmount;

    @TableField("CONTRACT_SIGN_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date contractSignTime;

    @TableField("CONTRACT_EFFECT_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date contractEffectTime;

    @TableField("CONTRACT_DEADLINE_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date contractDeadlineTime;

    @TableField("CONTRACT_CREATE_BY")
    private String contractCreateBy;

    @TableField("CONTRACT_CREATE_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date contractCreateTime;

    @TableField("TYPE")
    private String type;

    @TableField("COMPLETED_QUANTITY")
    private Integer completedQuantity;

    @TableField("COMPLETED_AMOUNT")
    private BigDecimal completedAmount;

    @TableField("PAY_TYPE")
    private String payType;

    @TableField("ORIGINAL_CONTRACT_CODE")
    private String originalContractCode;

    @TableField("IS_CHANGE")
    private String isChange;

    @TableField("CHANGE_REASON")
    private String changeReason;

    @TableField("CONTRACT_REMARKS")
    private String contractRemarks;

    @TableField("CONTRACT_STATUS")
    private String contractStatus;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;
}
