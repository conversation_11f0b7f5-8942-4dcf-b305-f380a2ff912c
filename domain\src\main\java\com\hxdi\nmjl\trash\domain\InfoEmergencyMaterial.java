package com.hxdi.nmjl.trash.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 应急物资
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("INFO_EMERGENCY_MATERIAL")
public class InfoEmergencyMaterial extends DataEntity<InfoEmergencyMaterial> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("STATION_ID")
    private Integer stationId;

    @TableField("STATION_NAME")
    private String stationName;

    @TableField("MATERIAL_TYPE")
    private String materialType;

    @TableField("MATERIAL_CATEGORY")
    private String materialCategory;

    @TableField("MATERIAL_NAME")
    private String materialName;

    @TableField("UNIT")
    private String unit;

    @TableField("QUANTITY")
    private BigDecimal quantity;

    @TableField("BRAND_NAME")
    private String brandName;

    @TableField("SPEC")
    private String spec;

    @TableField("PROVINCE_ID")
    private Integer provinceId;

    @TableField("PROVINCE")
    private String province;

    @TableField("CITY_ID")
    private Integer cityId;

    @TableField("CITY")
    private String city;

    @TableField("COUNTY_ID")
    private Integer countyId;

    @TableField("COUNTY")
    private String county;

    @TableField("ADDRESS")
    private String address;

    @TableField("ADMINISTRATOR_ID")
    private Integer administratorId;

    @TableField("ADMINISTRATOR_NAME")
    private String administratorName;

    @TableField("ADMINISTRATOR_CONTACT")
    private String administratorContact;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;

}
