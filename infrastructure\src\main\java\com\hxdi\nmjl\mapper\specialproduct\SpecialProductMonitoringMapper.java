package com.hxdi.nmjl.mapper.specialproduct;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.specialproduct.SpecialProductMonitoring;
import com.hxdi.nmjl.vo.condition.specialproduct.SpecialProductMonitoringCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 优特产品分析报告Mapper接口
 */
public interface SpecialProductMonitoringMapper extends SuperMapper<SpecialProductMonitoring> {

    Page<SpecialProductMonitoring> selectPageV1(Page<SpecialProductMonitoring> page, @Param("condition") SpecialProductMonitoringCondition condition);

    List<SpecialProductMonitoring> selectListV1(@Param("condition") SpecialProductMonitoringCondition condition);
}
