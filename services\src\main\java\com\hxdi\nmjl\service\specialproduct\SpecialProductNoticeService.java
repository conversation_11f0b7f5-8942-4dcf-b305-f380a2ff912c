package com.hxdi.nmjl.service.specialproduct;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.specialproduct.SpecialProductNotice;
import com.hxdi.nmjl.vo.condition.specialproduct.SpecialProductNoticeCondition;


import java.util.List;

/**
 * 地方特色产品征集公告服务接口
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/16
 */
public interface SpecialProductNoticeService extends IBaseService<SpecialProductNotice> {

    /**
     * 新增征集公告
     * @param notice 征集公告实体
     */
    void create(SpecialProductNotice notice);

    /**
     * 修改征集公告
     * @param notice 征集公告实体
     */
    void update(SpecialProductNotice notice);

    /**
     * 获取公告详情
     * @param noticeId 公告ID
     * @return 征集公告实体
     */
    SpecialProductNotice getDetail(String noticeId);

    /**
     * 分页查询公告
     * @param condition 查询条件
     * @return 分页结果
     */
    Page<SpecialProductNotice> pages(SpecialProductNoticeCondition condition);

    /**
     * 列表查询公告
     * @param condition 查询条件
     * @return 公告列表
     */
    List<SpecialProductNotice> lists(SpecialProductNoticeCondition condition);

    /**
     * 删除公告
     * @param noticeId 公告ID
     */
    void remove(String noticeId);

    /**
     * 发布公告
     * @param noticeId 公告ID
     */
    void publish(String noticeId);

    /**
     * 撤销公告
     * @param noticeId 公告ID
     */
    void revoke(String noticeId);

}
