package com.hxdi.nmjl.trash.domain.product;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 生产加工订单
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("BUSI_TMP_PROCESS_ORDER")
public class BusiTmpProcessOrder extends DataEntity<BusiTmpProcessOrder> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("CODE")
    private String code;

    @TableField("PLAN_ID")
    private Integer planId;

    @TableField("PLAN_CODE")
    private String planCode;

    @TableField("CONTRACT_TYPE")
    private String contractType;

    @TableField("CONTRACT_CODE")
    private String contractCode;

    @TableField("CONTRACT_TITLE")
    private String contractTitle;

    @TableField("ENTERPRISE_ID")
    private Integer enterpriseId;

    @TableField("ENTERPRISE_NAME")
    private String enterpriseName;

    @TableField("CONTRACT_CUSTOMER_ID")
    private Integer contractCustomerId;

    @TableField("CONTRACT_CUSTOMER_CODE")
    private String contractCustomerCode;

    @TableField("CONTRACT_CUSTOMER_NAME")
    private String contractCustomerName;

    @TableField("TOTAL_AMOUNT")
    private BigDecimal totalAmount;

    @TableField("PAY_TYPE")
    private String payType;

    @TableField("DELIVERY_TYPE")
    private String deliveryType;

    @TableField("CONTRACT_SIGN_NAME")
    private String contractSignName;

    @TableField("CONTRACT_SIGN_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date contractSignTime;

    @TableField("CONTRACT_EFFECT_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date contractEffectTime;

    @TableField("CONTRACT_END_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date contractEndTime;

    @TableField("CONTRACT_STATUS")
    private String contractStatus;

    @TableField("SETTLE_STATUS")
    private String settleStatus;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;
}
