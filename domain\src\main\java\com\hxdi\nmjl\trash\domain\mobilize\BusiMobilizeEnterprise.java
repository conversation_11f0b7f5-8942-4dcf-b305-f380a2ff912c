package com.hxdi.nmjl.trash.domain.mobilize;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 动员企业
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("BUSI_MOBILIZE_ENTERPRISE")
public class BusiMobilizeEnterprise extends DataEntity<BusiMobilizeEnterprise> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("ENTERPRISE_TYPE")
    private Integer enterpriseType;

    @TableField("NAME")
    private String name;

    @TableField("PROVINCE_ID")
    private Integer provinceId;

    @TableField("PROVINCE")
    private String province;

    @TableField("CITY_ID")
    private Integer cityId;

    @TableField("CITY")
    private String city;

    @TableField("COUNTY_ID")
    private Integer countyId;

    @TableField("COUNTY")
    private String county;

    @TableField("ADDRESS")
    private String address;

    @TableField("MAIN_BUSI")
    private String mainBusi;

    @TableField("DIRECTOR")
    private String director;

    @TableField("CONTACT")
    private String contact;

    @TableField("DIRECTOR2")
    private String director2;

    @TableField("CONTACT2")
    private String contact2;

    @TableField("DIRECTOR3")
    private String director3;

    @TableField("CONTACT3")
    private String contact3;

    @TableField("COLD_DELIVERY")
    private BigDecimal coldDelivery;

    @TableField("NORMAL_DELIVERY")
    private BigDecimal normalDelivery;

    @TableField("RESPONSE_TIME")
    private String responseTime;

    @TableField("DELIVERY_AREA")
    private String deliveryArea;

    @TableField("PRODUCTION_ABILITY")
    private String productionAbility;

    @TableField("OTHER_SUPPORT")
    private String otherSupport;

    @TableField("CUSTOMER_CODE")
    private String customerCode;

    @TableField("PLAN_ID")
    private Integer planId;

    @TableField("PLAN_NAME")
    private String planName;

    @TableField("PLAN_DOCUMENT_NUMBER")
    private String planDocumentNumber;

    @TableField("MANAGEMENT_ID")
    private Integer managementId;

    @TableField("MANAGEMENT_NAME")
    private String managementName;

    @TableField("MANAGEMENT_CODE")
    private String managementCode;

    @TableField("PLAN_LEVEL")
    private String planLevel;

    @TableField("PLAN_STATUS")
    private String planStatus;

    @TableField("EMERGENCY_FUND")
    private String emergencyFund;

    @TableField("SET_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date setTime;

    @TableField("ISSUE_UNIT")
    private String issueUnit;

    @TableField("CURRENT_VERSION")
    private String currentVersion;

    @TableField("PLAN_TEXT_CONTENT")
    private String planTextContent;

    @TableField("PLAN_TYPE")
    private String planType;

    @TableField("GROUP_CODE")
    private String groupCode;

    @TableField("GROUP_NAME")
    private String groupName;

    @TableField("DUTY_PHONE")
    private String dutyPhone;

    @TableField("GROUP_LEADER")
    private String groupLeader;

    @TableField("GROUP_LEADER_PHONE")
    private String groupLeaderPhone;

    @TableField("ENTRY_PERSON")
    private String entryPerson;

    @TableField("GROUP_MEMBERS")
    private String groupMembers;

    @TableField("WORK_CONTENT")
    private String workContent;

    @TableField("TOTAL_COUNT")
    private Integer totalCount;

    @TableField("FEMALE_COUNT")
    private Integer femaleCount;

    @TableField("MINORITY_COUNT")
    private Integer minorityCount;

    @TableField("COMMUNIST_COUNT")
    private Integer communistCount;

    @TableField("ON_DUTY_COUNT")
    private Integer onDutyCount;

    @TableField("MANAGER_COUNT")
    private Integer managerCount;

    @TableField("TECHNICAL_COUNT")
    private Integer technicalCount;

    @TableField("SKILLED_WORKER_COUNT")
    private Integer skilledWorkerCount;

    @TableField("PERMANENT_COUNT")
    private Integer permanentCount;

    @TableField("TEMP_COUNT")
    private Integer tempCount;

    @TableField("OTHER_COUNT")
    private Integer otherCount;

    @TableField("POSTGRADUATE_COUNT")
    private Integer postgraduateCount;

    @TableField("UNDERGRADUATE_COUNT")
    private Integer undergraduateCount;

    @TableField("COLLEGE_COUNT")
    private Integer collegeCount;

    @TableField("TECHNICAL_SECONDARY_COUNT")
    private Integer technicalSecondaryCount;

    @TableField("HIGH_COUNT")
    private Integer highCount;

    @TableField("JUNIOR_COUNT")
    private Integer juniorCount;

    @TableField("AGE_UNDER_35_COUNT")
    private Integer ageUnder35Count;

    @TableField("AGE_36_45_COUNT")
    private Integer age3645Count;

    @TableField("AGE_46_54_COUNT")
    private Integer age4654Count;

    @TableField("AGE_ON_55_COUNT")
    private Integer ageOn55Count;

    @TableField("LONGITUDE")
    private BigDecimal longitude;

    @TableField("LATITUDE")
    private BigDecimal latitude;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;
}
