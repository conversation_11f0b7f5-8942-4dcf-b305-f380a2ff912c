<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.inventory.StorageCapacityConversionMapper">

    <!-- 基础字段列表 -->
    <sql id="Base_Column_List">
        ID, STORAGE_CAP_ID, ALLOCATE_CAP, USED_CAP, AVAILABLE_CAP,
        CLIENT_ID, CLIENT_NAME, EXPIRATION_DATE, OPT_TIME, OPT_EMP,
        STATE, ATTACHEMENTS, ENABLED, APPROVER, APPROVE_TIME,
        APPROVE_OPINION, TENANT_ID, DATA_HIERARCHY_ID,APPROVE_STATUS
    </sql>

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.inventory.StorageCapacityConversion">
        <id column="ID" property="id"/>
        <result column="STORAGE_CAP_ID" property="storageCapId"/>
        <result column="ALLOCATE_CAP" property="allocateCap"/>
        <result column="USED_CAP" property="usedCap"/>
        <result column="AVAILABLE_CAP" property="availableCap"/>
        <result column="CLIENT_ID" property="clientId"/>
        <result column="CLIENT_NAME" property="clientName"/>
        <result column="EXPIRATION_DATE" property="expirationDate"/>
        <result column="OPT_TIME" property="optTime"/>
        <result column="OPT_EMP" property="optEmp"/>
        <result column="STATE" property="state"/>
        <result column="ATTACHEMENTS" property="attachments"/>
        <result column="ENABLED" property="enabled"/>
        <result column="APPROVER" property="approver"/>
        <result column="APPROVE_TIME" property="approveTime"/>
        <result column="APPROVE_OPINION" property="approveOpinion"/>
        <result column="TENANT_ID" property="tenantId"/>
        <result column="DATA_HIERARCHY_ID" property="dataHierarchyId"/>
        <result column="APPROVE_STATUS" property="approveStatus"/>
    </resultMap>

    <!-- getList 查询 -->
    <select id="getList" parameterType="com.hxdi.nmjl.condition.inventory.StorageCapacityConversionCondition"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        B_STORAGE_CAPACITY_CONVERSION
        <where>
            ENABLED = 1
            <if test="clientName != null and clientName != ''">
                AND CLIENT_NAME LIKE CONCAT('%', #{clientName}, '%')
            </if>
            <if test="optTime != null">
                AND OPT_TIME = #{optTime}
            </if>
            <if test="state != null">
                AND STATE = #{state}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <!-- getPage 查询 -->
    <select id="getPages" parameterType="com.hxdi.nmjl.condition.inventory.StorageCapacityConversionCondition"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        B_STORAGE_CAPACITY_CONVERSION
        <where>
            ENABLED = 1
            <if test="clientName != null and clientName != ''">
                AND CLIENT_NAME LIKE CONCAT('%', #{clientName}, '%')
            </if>
            <if test="condition.startTime != null">
                and OPT_TIME &gt;= #{condition.startTime}
            </if>
            <if test="condition.endTime != null">
                and OPT_TIME &lt;= #{condition.endTime}
            </if>
            <if test="state != null">
                AND STATE = #{state}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>


</mapper>
