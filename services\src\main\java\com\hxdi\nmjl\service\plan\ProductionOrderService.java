package com.hxdi.nmjl.service.plan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.inout.InoutDetail;
import com.hxdi.nmjl.domain.plan.ProductionOrder;
import com.hxdi.nmjl.condition.plan.ProductionOrderCondition;
import com.hxdi.nmjl.domain.plan.ProductionOrderTrace;

import java.util.List;

public interface ProductionOrderService extends IBaseService<ProductionOrder> {

    /**
     * 获取生产订单详情
     * @param id 生产订单ID
     * @return 生产订单详情
     */
    ProductionOrder getDetail(String id);

    /**
     * 根据条件查询生产订单列表（带数据权限）
     * @param condition 查询条件
     * @return
     */
    List<ProductionOrder> listV1(ProductionOrderCondition condition);

    /**
     * 根据条件查询生产订单列表
     * @param condition 查询条件
     * @return
     */
     List<ProductionOrder> listV2(ProductionOrderCondition condition);

    /**
     * 根据条件查询生产订单分页列表
     * @param condition 查询条件
     * @return
     */
    Page<ProductionOrder> pageV1(ProductionOrderCondition condition);

    /**
     * 新增生产订单
     * @param productionOrder 生产订单
     */
    void createV1(ProductionOrder productionOrder);

    /**
     * 修改生产订单
     * @param productionOrder 生产订单
     */
    void updateV1(ProductionOrder productionOrder);

    /**
     * 提交生产订单
     * @param id 生产订单ID
     */
    void submitV1(String id);

    /**
     * 删除生产订单
     * @param id 生产订单ID
     */
    void removeV1(String id);


    /**
     * 填报订单对应的各个品种的生产情况
     * 注意：此处会更新合同、标的、筹措计划的完成情况
     * @param traceList 订单跟踪记录列表
     */
    void fillOrder(List<ProductionOrderTrace> traceList);

    void generateOrderStatistics(List<InoutDetail> inoutDetailList);
}
