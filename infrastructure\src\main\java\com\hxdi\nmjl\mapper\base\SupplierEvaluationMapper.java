package com.hxdi.nmjl.mapper.base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.domain.base.SupplierEvaluation;
import com.hxdi.nmjl.vo.condition.base.SupplierEvaluationCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 供应商评价 Mapper 接口
 */
public interface SupplierEvaluationMapper extends SuperMapper<SupplierEvaluation> {

    /**
     * 列表查询供应商评价信息
     *
     * @param condition 查询条件
     * @return List<SupplierEvaluation>
     */
    List<SupplierEvaluation> getList(@Param("condition") SupplierEvaluationCondition condition);

    /**
     * 分页查询供应商评价信息
     *
     * @param condition 查询条件
     * @param page      分页对象
     * @return Page<SupplierEvaluation>
     */
    Page<SupplierEvaluation> getPages(@Param("condition") SupplierEvaluationCondition condition, @Param("page") Page<SupplierEvaluation> page);
}
