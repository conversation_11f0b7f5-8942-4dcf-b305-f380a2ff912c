package com.hxdi.nmjl.mapper.emergency;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.condition.emergency.EmergencyScheduleCondition;
import com.hxdi.nmjl.domain.emergency.EmergencySchedule;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface EmergencyScheduleMapper extends SuperMapper<EmergencySchedule> {

    Page<EmergencySchedule> selectPageV1(Page<EmergencySchedule> page, @Param("condition") EmergencyScheduleCondition condition);

    List<EmergencySchedule> selectListV1(@Param("condition") EmergencyScheduleCondition condition);
}