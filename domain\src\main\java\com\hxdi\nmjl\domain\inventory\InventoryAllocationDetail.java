package com.hxdi.nmjl.domain.inventory;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.hxdi.nmjl.base.Entity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * 库存调配明细
 * @TableName B_INVENTORY_ALLOCATION_DETAIL
 */
@TableName(value ="B_INVENTORY_ALLOCATION_DETAIL")
@Getter
@Setter
public class InventoryAllocationDetail extends Entity<InventoryAllocationDetail> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "ID",type = IdType.ASSIGN_ID)
    @ApiModelProperty(value="主键")
    private String id;

    /**
     * 调配ID
     */
    @TableField(value = "ALLOCATE_ID")
    @ApiModelProperty(value="调配单号")
    private String allocateId;

    /**
     * 生产批次号
     */
    @TableField(value = "BATCH_NUM")
    @ApiModelProperty(value="生产批次号")
    private String batchNum;

    /**
     * 品种ID
     */
    @TableField(value = "CATALOG_ID")
    @ApiModelProperty(value="品种ID")
    private String catalogId;

    /**
     * 品种名称
     */
    @TableField(value = "CATALOG_NAME")
    @ApiModelProperty(value="品种名称")
    private String catalogName;

    /**
     * 品牌名称
     */
    @TableField(value = "BRAND")
    @ApiModelProperty(value="品牌名称")
    private String brand;

    /**
     * 质量等级：字典：YZLDJ/LZLDJ
     */
    @TableField(value = "GRADE")
    @ApiModelProperty(value="质量等级")
    private String grade;

    /**
     * 规格
     */
    @TableField(value = "SPECIFICATION")
    @ApiModelProperty(value="规格")
    private String specification;

    /**
     * 生产日期
     */
    @TableField(value = "PRODUCT_DATE")
    @ApiModelProperty(value="生产日期")
    private Date productDate;

    /**
     * 计划数量
     */
    @TableField(value = "PLAN_QTY")
    @ApiModelProperty(value="计划数量")
    private BigDecimal planQty;

    /**
     * 完成数量
     */
    @TableField(value = "COMPLETED_QTY")
    @ApiModelProperty(value="完成数量")
    private BigDecimal completedQty;

    /**
     * 储备性质:字典CBXZ
     */
    @TableField(value = "RESERVE_LEVEL")
    @ApiModelProperty(value="储备性质")
    private String reserveLevel;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID")
    @ApiModelProperty(value="租户id")
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID")
    @ApiModelProperty(value="组织")
    private String dataHierarchyId;


}