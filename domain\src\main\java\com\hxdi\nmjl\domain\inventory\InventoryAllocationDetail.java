package com.hxdi.nmjl.domain.inventory;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 库存调配明细
 * @TableName B_INVENTORY_ALLOCATION_DETAIL
 */
@TableName(value ="B_INVENTORY_ALLOCATION_DETAIL")
@Data
public class InventoryAllocationDetail {
    /**
     * 主键
     */
    @TableId
    private String id;

    /**
     * 调配ID
     */
    private String allocateId;

    /**
     * 生产批次号
     */
    private String batchNum;

    /**
     * 品种ID
     */
    private String catalogId;

    /**
     * 品种名称
     */
    private String catalogName;

    /**
     * 品牌名称
     */
    private String brand;

    /**
     * 质量等级：字典：YZLDJ/LZLDJ
     */
    private String grade;

    /**
     * 规格
     */
    private String specification;

    /**
     * 生产日期
     */
    private Date productDate;

    /**
     * 计划数量
     */
    private BigDecimal planQty;

    /**
     * 完成数量
     */
    private BigDecimal completedQty;

    /**
     * 储备性质:字典CBXZ
     */
    private String reserveLevel;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 组织
     */
    private String dataHierarchyId;


}