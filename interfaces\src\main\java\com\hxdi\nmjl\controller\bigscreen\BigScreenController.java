package com.hxdi.nmjl.controller.bigscreen;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.nmjl.condition.bigscreen.OrderMonthRecordCondition;
import com.hxdi.nmjl.condition.inventory.InventoryCondition;
import com.hxdi.nmjl.condition.msg.WarningInfoCondition;
import com.hxdi.nmjl.condition.plan.ProcurementPlanCondition;
import com.hxdi.nmjl.domain.bigscreen.BigScreenConfig;
import com.hxdi.nmjl.domain.bigscreen.OrgPoint;
import com.hxdi.nmjl.domain.common.WarningInfo;
import com.hxdi.nmjl.domain.inventory.Inventory;
import com.hxdi.nmjl.service.bigscreen.BigScreenService;
import com.hxdi.nmjl.vo.bigscreen.AreaOrderStatVO;
import com.hxdi.nmjl.vo.bigscreen.InventorySumAndCapacityVO;
import com.hxdi.nmjl.vo.bigscreen.ProcurementPlanSummaryVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @program: nmjl-service
 * @description: 大屏接口
 * @author: 王贝强
 * @create: 2025-07-29 15:39
 */
@Api(tags = "大屏接口")
@RestController
@RequestMapping("/bigScreen")
public class BigScreenController {

    @Resource
    private BigScreenService bigScreenService;

    @GetMapping("/screenConfig")
    @ApiOperation("根据配置Key获取对应的大屏配置")
    public ResultBody<List<BigScreenConfig>> getScreenConfig(@RequestParam(value = "ConfigKey") String ConfigKey) {
        return ResultBody.<List<BigScreenConfig>>OK().data(bigScreenService.getScreenConfig(ConfigKey));
    }

    @GetMapping("/areaMonthlyOrderSummary")
    @ApiOperation("获取每月订单汇总统计数据")
    public ResultBody<List<AreaOrderStatVO>> getAreaMonthlyOrderSummary(OrderMonthRecordCondition condition) {
        return ResultBody.<List<AreaOrderStatVO>>OK().data(bigScreenService.getAreaMonthlyOrderSummary(condition));
    }

    @GetMapping("/areaMonthlyCategoryOrderSummary")
    @ApiOperation("获取每月按品种分类的供销汇总统计数据")
    public ResultBody<List<AreaOrderStatVO>> getAreaMonthlyCategoryOrderSummary(OrderMonthRecordCondition condition) {
        return ResultBody.<List<AreaOrderStatVO>>OK().data(bigScreenService.getAreaMonthlyCategoryOrderSummary(condition));
    }

    @GetMapping("/orgPoint")
    @ApiOperation("获取该区域下各类机构的点位")
    public ResultBody<List<OrgPoint>> getOrgPoint(@RequestParam(required = false) String areaCode) {
        return ResultBody.<List<OrgPoint>>OK().data(bigScreenService.getOrgPoint(areaCode));
    }

    @GetMapping("/orgNum")
    @ApiOperation("获取该区域下各类机构的数量")
    public ResultBody<Map<Integer, Integer>> getOrgNum(@RequestParam(required = false) String areaCode) {
        return ResultBody.<Map<Integer, Integer>>OK().data(bigScreenService.getOrgNum(areaCode));
    }

    @GetMapping("/warningInfoPage")
    @ApiOperation("获取预警信息分页列表")
    public ResultBody<Page<WarningInfo>> getWarningInfoList(WarningInfoCondition condition) {
        return ResultBody.<Page<WarningInfo>>OK().data(bigScreenService.getWarningInfoPage(condition));
    }

    @GetMapping("/inventorySummary")
    @ApiOperation("获取库存总览数据")
    public ResultBody<Map<String, BigDecimal>> getInventorySummary(@RequestParam(required = false, value = "areaCode") String areaCode) {
        return ResultBody.<Map<String, BigDecimal>>OK().data(bigScreenService.getInventorySummary(areaCode));
    }

    @GetMapping("/inventorySummaryPage")
    @ApiOperation("获取库存总览分页数据")
    public ResultBody<Page<Inventory>> getInventorySummaryPage(InventoryCondition condition) {
        return ResultBody.<Page<Inventory>>OK().data(bigScreenService.getInventorySummaryPage(condition));
    }

    @GetMapping("/inventorySumAndCapacity")
    @ApiOperation("获取仓储能力总览数据")
    public ResultBody<List<InventorySumAndCapacityVO>> getInventorySumAndCapacity(@RequestParam(required = false, value = "areaCode") String areaCode) {
        return ResultBody.<List<InventorySumAndCapacityVO>>OK().data(bigScreenService.getInventorySumAndCapacity(areaCode));
    }

    @GetMapping("/inventorySumAndCapacityPage")
    @ApiOperation("获取仓储能力分页数据")
    public ResultBody<Page<InventorySumAndCapacityVO>> getInventorySumAndCapacityPage(@RequestParam(required = false, value = "areaCode") String areaCode,
                                                                                      @RequestParam(required = false, value = "storeId") String storeId) {
        return ResultBody.<Page<InventorySumAndCapacityVO>>OK().data(bigScreenService.getInventorySumAndCapacityPage(areaCode, storeId));
    }

    @GetMapping("/randomPlanSummary")
    @ApiOperation("获取随机城市的筹措计划总览")
    public ResultBody<ProcurementPlanSummaryVO> getRandomPlanSummary(@RequestParam(value = "areaCode") String areaCode) {
        return ResultBody.<ProcurementPlanSummaryVO>OK().data(bigScreenService.getPlanSummary(areaCode));
    }

    @GetMapping("/PlanSummaryPage")
    @ApiOperation("获取指定地区下的各个军供站的各个品类的筹措计划")
    public ResultBody<Page<ProcurementPlanSummaryVO>> getRandomPlanSummaryPage(ProcurementPlanCondition condition) {
        return ResultBody.<Page<ProcurementPlanSummaryVO>>OK().data(bigScreenService.getPlanSummaryPage(condition));
    }
}
