package com.hxdi.nmjl.service.bigscreen;

import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.bigscreen.OrderMonthRecordCondition;
import com.hxdi.nmjl.domain.bigscreen.BigScreenConfig;
import com.hxdi.nmjl.domain.bigscreen.InfoArea;
import com.hxdi.nmjl.domain.bigscreen.OrderMonthRecord;
import com.hxdi.nmjl.domain.plan.SaleOrder;
import com.hxdi.nmjl.domain.plan.SaleOrderItem;
import com.hxdi.nmjl.domain.plan.ProductionOrder;
import com.hxdi.nmjl.domain.plan.ProductionOrderItem;
import com.hxdi.nmjl.mapper.bigscreen.OrderMonthRecordMapper;
import com.hxdi.nmjl.service.plan.ProductionOrderService;
import com.hxdi.nmjl.service.plan.SaleOrderService;
import com.hxdi.nmjl.vo.bigscreen.AreaOrderStatVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: nmjl-service
 * @description: 购销订单统计服务
 * @author: 王贝强
 * @create: 2025-07-28 15:17
 */
@Service
@Slf4j
public class OrderMonthRecordService extends BaseServiceImpl<OrderMonthRecordMapper, OrderMonthRecord> {

    @Resource
    private InfoAreaService infoAreaService;

    @Resource
    private SaleOrderService saleOrderService;

    @Resource
    private ProductionOrderService productionOrderService;

    /**
     * 获取区域每月订单汇总统计数据(默认统计内蒙古自治区)
     *
     * @param condition 查询条件
     * @return 每月汇总统计数据列表
     */
    public List<AreaOrderStatVO> getAreaMonthlyOrderSummary(OrderMonthRecordCondition condition) {
        if (CommonUtils.isEmpty(condition.getAreaCodes())) {
            condition.setAreaCodes("150000");
        }

        // 解析区域代码
        List<String> areaCodes = parseAreaCodes(condition.getAreaCodes());

        // 根据是否包含子区域来处理区域代码
        List<String> finalAreaCodes = processAreaCodes(areaCodes, condition.getIsIncludeChild());

        // 设置最终的区域代码列表
        condition.setCodes(finalAreaCodes);

        // 查询订单数据
        List<OrderMonthRecord> monthRecordList = baseMapper.selectListV1(condition);

        // 按月份分组统计
        return buildMonthlyAreaOrderStats(areaCodes, monthRecordList, condition);
    }

    /**
     * 获取区域每月按品种分类的订单汇总统计数据(默认统计内蒙古自治区)
     *
     * @param condition 查询条件
     * @param list      品种分类配置
     * @return 每月汇总统计数据列表
     */
    public List<AreaOrderStatVO> getAreaMonthlyCategoryOrderSummary(OrderMonthRecordCondition condition, List<BigScreenConfig> list) {
        if (CommonUtils.isEmpty(condition.getAreaCodes())) {
            condition.setAreaCodes("150000");
        }
        if (list.isEmpty()) {
            return Collections.emptyList();
        }

        // 解析区域代码
        List<String> areaCodes = parseAreaCodes(condition.getAreaCodes());

        // 根据是否包含子区域来处理区域代码
        List<String> finalAreaCodes = processAreaCodes(areaCodes, condition.getIsIncludeChild());

        // 设置最终的区域代码列表
        condition.setCodes(finalAreaCodes);

        // 查询订单数据
        List<OrderMonthRecord> monthRecordList = baseMapper.selectListV1(condition);

        // 构建品种分类映射：品种ID -> 分类配置
        Map<String, BigScreenConfig> catalogToCategoryMap = new HashMap<>();
        // 收集所有分类名称（包括没有品种的分类）
        Set<String> allCategories = new HashSet<>();

        for (BigScreenConfig config : list) {
            allCategories.add(config.getDesc()); // 确保所有配置的分类都被包含
            if (config.getCatalogId() != null && !config.getCatalogId().isEmpty()) {
                for (String catalogId : config.getCatalogId()) {
                    catalogToCategoryMap.put(catalogId, config);
                }
            }
        }

        // 按月份和品种分类分组统计
        return buildMonthlyCategoryAreaOrderStats(areaCodes, monthRecordList, catalogToCategoryMap, allCategories, condition);
    }

    /**
     * 解析区域代码字符串为列表
     */
    private List<String> parseAreaCodes(String areaCodesStr) {
        if (areaCodesStr.contains(",")) {
            return Arrays.stream(areaCodesStr.split(","))
                    .map(String::trim)
                    .filter(code -> !code.isEmpty())
                    .collect(Collectors.toList());
        } else {
            return Collections.singletonList(areaCodesStr.trim());
        }
    }

    /**
     * 处理区域代码，根据是否包含子区域来扩展代码列表
     */
    private List<String> processAreaCodes(List<String> areaCodes, String isIncludeChild) {
        Set<String> resultCodes = new LinkedHashSet<>(areaCodes);

        if ("1".equals(isIncludeChild)) {
            // 需要包含子区域
            for (String areaCode : areaCodes) {
                List<String> childCodes = infoAreaService.getAllChildAreaCodes(areaCode);
                resultCodes.addAll(childCodes);
            }
        }

        return new ArrayList<>(resultCodes);
    }

    /**
     * 计算区域统计数据
     */
    private void calculateAreaStats(AreaOrderStatVO statVO, List<OrderMonthRecord> records) {
        if (records == null || records.isEmpty()) {
            statVO.setPurchaseOrderCount(0);
            statVO.setSaleOrderCount(0);
            statVO.setPurchaseOrderQty(BigDecimal.ZERO);
            statVO.setSaleOrderQty(BigDecimal.ZERO);
            statVO.setPurchaseCompletedQty(BigDecimal.ZERO);
            statVO.setSaleCompletedQty(BigDecimal.ZERO);
            return;
        }

        // 分组统计采购和销售订单
        Map<String, List<OrderMonthRecord>> typeGroups = records.stream()
                .collect(Collectors.groupingBy(OrderMonthRecord::getOrderType));

        // 采购订单统计（订单类型 1->采购订单）
        List<OrderMonthRecord> purchaseOrders = typeGroups.getOrDefault("1", Collections.emptyList());
        statVO.setPurchaseOrderCount(purchaseOrders.size());
        statVO.setPurchaseOrderQty(purchaseOrders.stream()
                .map(record -> parseDecimal(record.getOrderQty()))
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        statVO.setPurchaseCompletedQty(purchaseOrders.stream()
                .map(record -> parseDecimal(record.getCompletedQty()))
                .reduce(BigDecimal.ZERO, BigDecimal::add));

        // 销售订单统计（订单类型 2->销售订单）
        List<OrderMonthRecord> saleOrders = typeGroups.getOrDefault("2", Collections.emptyList());
        statVO.setSaleOrderCount(saleOrders.size());
        statVO.setSaleOrderQty(saleOrders.stream()
                .map(record -> parseDecimal(record.getOrderQty()))
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        statVO.setSaleCompletedQty(saleOrders.stream()
                .map(record -> parseDecimal(record.getCompletedQty()))
                .reduce(BigDecimal.ZERO, BigDecimal::add));
    }

    /**
     * 安全解析字符串为BigDecimal
     */
    private BigDecimal parseDecimal(String value) {
        if (value == null || value.trim().isEmpty()) {
            return BigDecimal.ZERO;
        }
        try {
            return new BigDecimal(value);
        } catch (NumberFormatException e) {
            return BigDecimal.ZERO;
        }
    }

    /**
     * 构建每月区域订单统计数据
     */
    private List<AreaOrderStatVO> buildMonthlyAreaOrderStats(List<String> areaCodes, List<OrderMonthRecord> monthRecordList, OrderMonthRecordCondition condition) {
        // 按统计日期（年月）分组
        Map<String, List<OrderMonthRecord>> monthlyRecordsMap = monthRecordList.stream()
                .filter(record -> record.getStatisticalDate() != null)
                .collect(Collectors.groupingBy(record -> {
                    // 提取年月，格式：2024-01
                    String date = record.getStatisticalDate();
                    if (date.length() >= 7) {
                        return date.substring(0, 7);
                    }
                    return date;
                }));

        // 获取所有月份（根据查询条件的时间范围生成）
        Set<String> allMonths = new HashSet<>(monthlyRecordsMap.keySet());

        // 根据查询条件补充时间范围内的所有月份
        if (condition.getStartTime() != null && condition.getEndTime() != null) {
            try {
                LocalDate startDate = LocalDate.parse(condition.getStartTime().substring(0, 7) + "-01");
                LocalDate endDate = LocalDate.parse(condition.getEndTime().substring(0, 7) + "-01");

                LocalDate current = startDate;
                while (!current.isAfter(endDate)) {
                    allMonths.add(current.toString().substring(0, 7));
                    current = current.plusMonths(1);
                }
            } catch (Exception e) {
                // 解析失败时，如果没有数据则添加当前月份
                if (allMonths.isEmpty()) {
                    String currentMonth = java.time.LocalDate.now().toString().substring(0, 7);
                    allMonths.add(currentMonth);
                }
            }
        } else if (allMonths.isEmpty()) {
            // 如果没有时间条件且没有数据，添加当前月份
            String currentMonth = java.time.LocalDate.now().toString().substring(0, 7);
            allMonths.add(currentMonth);
        }

        List<AreaOrderStatVO> result = new ArrayList<>();

        // 为每个传入的区域代码创建月度统计
        for (String areaCode : areaCodes) {
            InfoArea area = infoAreaService.getByAreaCode(areaCode);
            if (area == null) {
                continue;
            }

            // 为每个月份创建统计（包括没有数据的月份）
            for (String month : allMonths) {
                List<OrderMonthRecord> monthRecords = monthlyRecordsMap.getOrDefault(month, new ArrayList<>());

                // 过滤出属于当前区域的记录
                List<OrderMonthRecord> areaRecords = monthRecords.stream()
                        .filter(record -> areaCode.equals(record.getAreaCode()))
                        .collect(Collectors.toList());

                AreaOrderStatVO monthlyStatVO = new AreaOrderStatVO();

                // 设置区域信息
                //monthlyStatVO.setArea(area);
                // 设置月份字段
                monthlyStatVO.setMonth(month);
                monthlyStatVO.setOrderRecords(areaRecords);
                monthlyStatVO.setChildren(null); // 不返回子节点

                // 计算统计数据（如果没有记录，会返回0值）
                calculateAreaStats(monthlyStatVO, areaRecords);

                result.add(monthlyStatVO);
            }
        }

        // 按月份排序
        result.sort(Comparator.comparing(AreaOrderStatVO::getMonth));

        return result;
    }

    /**
     * 构建每月按品种分类的区域订单统计数据
     */
    private List<AreaOrderStatVO> buildMonthlyCategoryAreaOrderStats(List<String> areaCodes,
                                                                     List<OrderMonthRecord> monthRecordList,
                                                                     Map<String, BigScreenConfig> catalogToCategoryMap,
                                                                     Set<String> allCategories,
                                                                     OrderMonthRecordCondition condition) {
        // 按统计日期（年月）分组
        Map<String, List<OrderMonthRecord>> monthlyRecordsMap = monthRecordList.stream()
                .filter(record -> record.getStatisticalDate() != null)
                .collect(Collectors.groupingBy(record -> {
                    String date = record.getStatisticalDate();
                    if (date.length() >= 7) {
                        return date.substring(0, 7);
                    }
                    return date;
                }));

        // 获取所有月份（根据查询条件的时间范围生成）
        Set<String> allMonths = new HashSet<>(monthlyRecordsMap.keySet());

        // 根据查询条件补充时间范围内的所有月份
        if (condition.getStartTime() != null && condition.getEndTime() != null) {
            try {
                LocalDate startDate = LocalDate.parse(condition.getStartTime().substring(0, 7) + "-01");
                LocalDate endDate = LocalDate.parse(condition.getEndTime().substring(0, 7) + "-01");

                LocalDate current = startDate;
                while (!current.isAfter(endDate)) {
                    allMonths.add(current.toString().substring(0, 7));
                    current = current.plusMonths(1);
                }
            } catch (Exception e) {
                // 解析失败时，如果没有数据则添加当前月份
                if (allMonths.isEmpty()) {
                    String currentMonth = java.time.LocalDate.now().toString().substring(0, 7);
                    allMonths.add(currentMonth);
                }
            }
        } else if (allMonths.isEmpty()) {
            // 如果没有时间条件且没有数据，添加当前月份
            String currentMonth = java.time.LocalDate.now().toString().substring(0, 7);
            allMonths.add(currentMonth);
        }

        List<AreaOrderStatVO> result = new ArrayList<>();

        // 为每个传入的区域代码创建月度分类统计
        for (String areaCode : areaCodes) {
            InfoArea area = infoAreaService.getByAreaCode(areaCode);
            if (area == null) {
                continue;
            }

            // 为每个月份创建统计
            for (String month : allMonths) {
                List<OrderMonthRecord> monthRecords = monthlyRecordsMap.getOrDefault(month, new ArrayList<>());

                // 过滤出属于当前区域的记录
                List<OrderMonthRecord> areaRecords = monthRecords.stream()
                        .filter(record -> areaCode.equals(record.getAreaCode()))
                        .collect(Collectors.toList());

                // 按品种分类分组
                Map<String, List<OrderMonthRecord>> categoryRecordsMap = areaRecords.stream()
                        .collect(Collectors.groupingBy(record -> {
                            BigScreenConfig category = catalogToCategoryMap.get(record.getCatalogId());
                            return category != null ? category.getDesc() : "其它";
                        }));

                // 为每个分类创建统计数据（包括没有数据的分类）
                for (String categoryName : allCategories) {
                    List<OrderMonthRecord> categoryRecords = categoryRecordsMap.getOrDefault(categoryName, new ArrayList<>());

                    AreaOrderStatVO categoryStatVO = new AreaOrderStatVO();
                    //categoryStatVO.setArea(area);
                    categoryStatVO.setMonth(month);
                    categoryStatVO.setCategory(categoryName);
                    categoryStatVO.setOrderRecords(categoryRecords);
                    categoryStatVO.setChildren(null);

                    // 计算统计数据（如果没有记录，会返回0值）
                    calculateAreaStats(categoryStatVO, categoryRecords);

                    result.add(categoryStatVO);
                }
            }
        }

        // 按月份和分类排序
        result.sort(Comparator.comparing(AreaOrderStatVO::getMonth)
                .thenComparing(AreaOrderStatVO::getCategory));

        return result;
    }

    /**
     * 生成订单月度统计数据
     * 根据传入参数精确匹配订单明细，并为匹配的订单明细生成一条订单统计记录
     *
     * @param orderType         订单类型(1->生产订单、2->销售订单)
     * @param id                订单ID
     * @param catalogId         品种ID
     * @param grade             质量等级
     * @param reserveLevel      储备类型
     * @param totalCompletedQty 完成数量
     * @param areaCode          区域编码
     */
    public void generateRecord(String orderType, String id, String catalogId, String grade, String reserveLevel, BigDecimal totalCompletedQty, String areaCode) {
        try {
            if ("2".equals(orderType)) {
                // 销售订单
                SaleOrder saleOrder = saleOrderService.getDetail(id);
                if (saleOrder == null) {
                    log.warn("未找到销售订单，订单ID: {}", id);
                    return;
                }

                // 根据传入参数精确匹配对应的订单明细
                SaleOrderItem matchedItem = null;
                if (saleOrder.getDetailList() != null && !saleOrder.getDetailList().isEmpty()) {
                    for (SaleOrderItem item : saleOrder.getDetailList()) {
                        if (catalogId.equals(item.getCatalogId()) &&
                            grade.equals(item.getGrade()) &&
                            reserveLevel.equals(item.getReserveLevel())) {
                            matchedItem = item;
                            break;
                        }
                    }
                }

                if (matchedItem == null) {
                    log.warn("未找到匹配的销售订单明细，订单ID: {}, catalogId: {}, grade: {}, reserveLevel: {}",
                            id, catalogId, grade, reserveLevel);
                    return;
                }

                // 只为匹配的明细生成一条统计记录
                OrderMonthRecord record = new OrderMonthRecord();

                // 基本信息
                record.setOrderCode(saleOrder.getOrderCode());
                record.setOrderType(orderType);
                record.setStatisticalDate(LocalDate.now().toString());

                // 组织信息
                record.setOrgId(saleOrder.getOrgId());
                record.setOrgName(saleOrder.getOrgName());
                record.setAreaCode(areaCode);  // 设置区域编码
                record.setStoreId(saleOrder.getStoreId());
                record.setStoreName(saleOrder.getStoreName());

                // 客户信息
                record.setClientId(saleOrder.getClientId());
                record.setClientName(saleOrder.getClientName());

                // 品种信息
                record.setCatalogId(matchedItem.getCatalogId());
                record.setCatalogName(matchedItem.getCatalogName());
                record.setBrand(matchedItem.getBrand());
                record.setGrade(matchedItem.getGrade());
                record.setSpecification(matchedItem.getSpecification());

                // 数量和金额信息
                record.setOrderQty(matchedItem.getOrderQty() != null ? matchedItem.getOrderQty().toString() : "0");
                record.setCompletedQty(totalCompletedQty != null ? totalCompletedQty.toString() : "0");
                record.setOrderPackQty(matchedItem.getOrderPackQty() != null ? matchedItem.getOrderPackQty().toString() : "0");
                record.setPrice(matchedItem.getPrice() != null ? matchedItem.getPrice().toString() : "0");

                // 其他信息
                record.setReserveLevel(matchedItem.getReserveLevel());
                record.setBatchNo(matchedItem.getBatchNo());
                record.setDataHierarchyId(saleOrder.getDataHierarchyId());

                // 保存记录
                this.save(record);
                
            } else if ("1".equals(orderType)) {
                // 生产订单
                ProductionOrder productionOrder = productionOrderService.getDetail(id);
                if (productionOrder == null) {
                    log.warn("未找到生产订单，订单ID: {}", id);
                    return;
                }

                // 根据传入参数精确匹配对应的订单明细
                ProductionOrderItem matchedItem = null;
                if (productionOrder.getDetailList() != null && !productionOrder.getDetailList().isEmpty()) {
                    for (ProductionOrderItem item : productionOrder.getDetailList()) {
                        if (catalogId.equals(item.getCatalogId()) &&
                            grade.equals(item.getGrade()) &&
                            reserveLevel.equals(item.getReserveLevel())) {
                            matchedItem = item;
                            break;
                        }
                    }
                }

                if (matchedItem == null) {
                    log.warn("未找到匹配的生产订单明细，订单ID: {}, catalogId: {}, grade: {}, reserveLevel: {}",
                            id, catalogId, grade, reserveLevel);
                    return;
                }

                // 只为匹配的明细生成一条统计记录
                OrderMonthRecord record = new OrderMonthRecord();

                // 基本信息
                record.setOrderCode(productionOrder.getOrderCode());
                record.setOrderType(orderType);
                record.setStatisticalDate(LocalDate.now().toString());

                // 组织信息
                record.setOrgId(productionOrder.getOrgId());
                record.setOrgName(productionOrder.getOrgName());
                record.setAreaCode(areaCode);  // 设置区域编码
                record.setStoreId(productionOrder.getStoreId());
                record.setStoreName(productionOrder.getStoreName());

                // 客户信息
                record.setClientId(productionOrder.getClientId());
                record.setClientName(productionOrder.getClientName());

                // 品种信息
                record.setCatalogId(matchedItem.getCatalogId());
                record.setCatalogName(matchedItem.getCatalogName());
                record.setBrand(matchedItem.getBrand());
                record.setGrade(matchedItem.getGrade());
                record.setSpecification(matchedItem.getSpecification());

                // 数量和金额信息
                record.setOrderQty(matchedItem.getOrderQty() != null ? matchedItem.getOrderQty().toString() : "0");
                record.setCompletedQty(totalCompletedQty != null ? totalCompletedQty.toString() : "0");
                record.setOrderPackQty(matchedItem.getOrderPackQty() != null ? matchedItem.getOrderPackQty().toString() : "0");
                record.setPrice(matchedItem.getPrice() != null ? matchedItem.getPrice().toString() : "0");

                // 其他信息
                record.setReserveLevel(matchedItem.getReserveLevel());
                record.setBatchNo(productionOrder.getBatchNo());  // 生产订单的批次号在主表中
                record.setDataHierarchyId(productionOrder.getDataHierarchyId());

                // 保存记录
                this.save(record);
                
            } else {
                return;
            }
            
            log.info("成功生成订单统计数据，订单类型: {}, 订单ID: {}, 完成数量: {}", 
                    orderType, id, totalCompletedQty);
                    
        } catch (Exception e) {
            log.error("生成订单统计数据失败，订单类型: {}, 订单ID: {}, 错误: {}", 
                    orderType, id, e.getMessage(), e);
        }
    }
}
