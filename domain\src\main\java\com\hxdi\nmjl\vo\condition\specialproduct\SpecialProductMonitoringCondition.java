package com.hxdi.nmjl.vo.condition.specialproduct;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@ApiModel(description = "优特产品分析报告查询条件")
@Getter
@Setter
public class SpecialProductMonitoringCondition extends QueryCondition {

    @ApiModelProperty(value = "产品ID")
    private String productId;

    @ApiModelProperty(value = "报告日期（开始）")
    private Date reportStartDate;

    @ApiModelProperty(value = "报告日期（结束）")
    private Date reportEndDate;

    @ApiModelProperty(value = "报告人")
    private String reporter;

}