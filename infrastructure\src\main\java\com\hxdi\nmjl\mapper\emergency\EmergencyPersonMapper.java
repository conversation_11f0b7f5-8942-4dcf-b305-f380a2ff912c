package com.hxdi.nmjl.mapper.emergency;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.condition.emergency.EmergencyPersonCondition;
import com.hxdi.nmjl.domain.emergency.EmergencyPerson;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface EmergencyPersonMapper extends SuperMapper<EmergencyPerson> {
    @DataPermission
    List<EmergencyPerson> getList(@Param("condition") EmergencyPersonCondition condition);
    @DataPermission
    Page<EmergencyPerson> getPages(@Param("condition") EmergencyPersonCondition condition, @Param("page") Page<EmergencyPerson> page);
}
