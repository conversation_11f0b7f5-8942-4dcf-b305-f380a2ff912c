package com.hxdi.nmjl.service.bigscreen;

import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.domain.bigscreen.InfoArea;
import com.hxdi.nmjl.mapper.bigscreen.InfoAreaMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @program: nmjl-service
 * @description: 区域信息服务
 * @author: 王贝强
 * @create: 2025-07-28 15:32
 */
@Service
@Slf4j
public class InfoAreaService extends BaseServiceImpl<InfoAreaMapper, InfoArea> {

    //区域缓存
    private static final Map<String, Object> CACHE = new ConcurrentHashMap<>();
    private static final String ALL_AREAS_KEY = "ALL_AREAS";
    private static final String AREA_CODE_MAP_KEY = "AREA_CODE_MAP";
    private static final String PARENT_CHILD_MAP_KEY = "PARENT_CHILD_MAP";
    private static final String ID_MAP_KEY = "ID_MAP";

    // 默认区域代码(内蒙古自治区)
    private static final String DEFAULT_AREA_CODE = "150000";

    /**
     * 清理所有缓存
     */
    public void clearCache() {
        CACHE.clear();
        log.info("区域信息缓存已清理");
    }

    /**
     * 清理特定缓存
     */
    public void clearCache(String key) {
        CACHE.remove(key);
        log.debug("已清理缓存: {}", key);
    }

    /**
     * 获取所有区域信息（带缓存）
     */
    @SuppressWarnings("unchecked")
    private List<InfoArea> getAllAreas() {
        return (List<InfoArea>) CACHE.computeIfAbsent(ALL_AREAS_KEY, k -> {
            try {
                List<InfoArea> areas = baseMapper.selectList(null);
                log.debug("从数据库加载区域信息，共{}条", areas.size());
                return areas;
            } catch (Exception e) {
                log.error("获取区域信息失败", e);
                throw new BaseException("获取区域信息失败: " + e.getMessage());
            }
        });
    }

    /**
     * 获取区域代码映射
     */
    @SuppressWarnings("unchecked")
    private Map<String, InfoArea> getAreaCodeMap() {
        return (Map<String, InfoArea>) CACHE.computeIfAbsent(AREA_CODE_MAP_KEY, k ->
                getAllAreas().stream()
                        .filter(area -> StringUtils.hasText(area.getAreaCode()))
                        .collect(Collectors.toMap(
                                InfoArea::getAreaCode,
                                area -> area,
                                (existing, replacement) -> {
                                    log.warn("发现重复的区域代码: {}", existing.getAreaCode());
                                    return existing;
                                }
                        ))
        );
    }

    /**
     * 获取父子关系映射
     */
    @SuppressWarnings("unchecked")
    private Map<Integer, List<InfoArea>> getParentChildMap() {
        return (Map<Integer, List<InfoArea>>) CACHE.computeIfAbsent(PARENT_CHILD_MAP_KEY, k ->
                getAllAreas().stream()
                        .filter(area -> area.getParentId() != null)
                        .collect(Collectors.groupingBy(InfoArea::getParentId))
        );
    }

    /**
     * 获取ID映射
     */
    @SuppressWarnings("unchecked")
    private Map<Integer, InfoArea> getIdMap() {
        return (Map<Integer, InfoArea>) CACHE.computeIfAbsent(ID_MAP_KEY, k ->
                getAllAreas().stream()
                        .filter(area -> area.getId() != null)
                        .collect(Collectors.toMap(
                                area -> safeParseId(area.getId()),
                                area -> area,
                                (existing, replacement) -> existing
                        ))
        );
    }

    /**
     * 安全的ID转换
     */
    private Integer safeParseId(String id) {
        if (!StringUtils.hasText(id)) {
            return null;
        }
        try {
            return Integer.valueOf(id);
        } catch (NumberFormatException e) {
            log.warn("无法解析区域ID: {}", id);
            return null;
        }
    }

    /**
     * 根据区域代码获取区域名称
     */
    public String getAreaNameByCode(String areaCode) {
        if (!StringUtils.hasText(areaCode)) {
            log.debug("区域代码为空，返回空字符串");
            return "";
        }

        try {
            InfoArea targetArea = getAreaCodeMap().get(areaCode);
            if (targetArea == null) {
                log.warn("未找到区域代码: {}", areaCode);
                return "";
            }

            return StringUtils.hasText(targetArea.getAreaName()) ? targetArea.getAreaName() : "";
        } catch (Exception e) {
            log.error("获取区域名称失败，areaCode: {}", areaCode, e);
            return "";
        }
    }

    /**
     * 获取完整的地区路径名称
     */
    public String getFullAreaNameByCode(String areaCode) {
        if (!StringUtils.hasText(areaCode)) {
            log.debug("区域代码为空，返回空字符串");
            return "";
        }

        try {
            InfoArea targetArea = getAreaCodeMap().get(areaCode);
            if (targetArea == null) {
                log.warn("未找到区域代码: {}", areaCode);
                return "";
            }

            return buildAreaPath(targetArea);
        } catch (Exception e) {
            log.error("构建区域路径失败，areaCode: {}", areaCode, e);
            return "";
        }
    }

    /**
     * 构建区域路径
     */
    private String buildAreaPath(InfoArea area) {
        List<String> pathNames = new ArrayList<>();
        Set<Integer> visited = new HashSet<>();
        InfoArea currentArea = area;
        Map<Integer, InfoArea> idMap = getIdMap();

        while (currentArea != null) {
            Integer currentId = safeParseId(currentArea.getId());
            if (currentId == null) {
                break;
            }

            // 防止循环引用
            if (visited.contains(currentId)) {
                log.warn("检测到区域循环引用，ID: {}", currentId);
                break;
            }
            visited.add(currentId);

            if (StringUtils.hasText(currentArea.getAreaName())) {
                pathNames.add(0, currentArea.getAreaName());
            }

            if (currentArea.getParentId() == null) {
                break;
            }

            currentArea = idMap.get(currentArea.getParentId());
        }

        return String.join("/", pathNames);
    }

    /**
     * 获取区域信息树
     */
    public List<InfoArea> buildTree(String areaCode, Integer subdistrict) {
        // 参数校验和默认值设置
        areaCode = StringUtils.hasText(areaCode) ? areaCode : DEFAULT_AREA_CODE;
        subdistrict = subdistrict != null ? Math.max(0, subdistrict) : 2;

        try {
            InfoArea rootArea = getAreaCodeMap().get(areaCode);
            if (rootArea == null) {
                log.warn("未找到根区域，areaCode: {}", areaCode);
                return Collections.emptyList();
            }

            return buildTreeFromRoot(rootArea, subdistrict);
        } catch (Exception e) {
            log.error("构建区域树失败，areaCode: {}, subdistrict: {}", areaCode, subdistrict, e);
            throw new BaseException("构建区域树失败: " + e.getMessage());
        }
    }

    /**
     * 从根节点构建树
     */
    private List<InfoArea> buildTreeFromRoot(InfoArea rootArea, Integer subdistrict) {
        if (subdistrict == 0) {
            // 只返回当前节点
            InfoArea clonedRoot = cloneArea(rootArea);
            setTreeLeaf(clonedRoot);
            return Collections.singletonList(clonedRoot);
        }

        // 递归获取子节点
        List<InfoArea> children = getChildrenRecursively(rootArea, subdistrict);
        setTreeLeafForList(children);
        return children;
    }

    /**
     * 递归获取子节点
     */
    private List<InfoArea> getChildrenRecursively(InfoArea parent, int maxDepth) {
        if (maxDepth <= 0 || parent == null) {
            return Collections.emptyList();
        }

        Integer parentId = safeParseId(parent.getId());
        if (parentId == null) {
            return Collections.emptyList();
        }

        List<InfoArea> directChildren = getParentChildMap().getOrDefault(parentId, Collections.emptyList());
        List<InfoArea> result = new ArrayList<>();

        for (InfoArea child : directChildren) {
            InfoArea clonedChild = cloneArea(child);

            if (maxDepth > 1) {
                List<InfoArea> grandChildren = getChildrenRecursively(child, maxDepth - 1);
                clonedChild.setChildren(grandChildren);
            }

            result.add(clonedChild);
        }

        return result;
    }

    /**
     * 克隆区域对象（避免修改缓存中的原始对象）
     */
    private InfoArea cloneArea(InfoArea original) {
        InfoArea cloned = new InfoArea();
        cloned.setId(original.getId());
        cloned.setAreaCode(original.getAreaCode());
        cloned.setAreaName(original.getAreaName());
        cloned.setParentId(original.getParentId());
        cloned.setTreeSort(original.getTreeSort());
        cloned.setTreeLevel(original.getTreeLevel());
        cloned.setTreeLeaf(original.getTreeLeaf());
        // 注意：children字段不应该在克隆时复制，应该重新设置
        return cloned;
    }

    /**
     * 设置单个节点的叶子状态
     */
    private void setTreeLeaf(InfoArea area) {
        if (area == null) {
            return;
        }

        Integer areaId = safeParseId(area.getId());
        boolean hasChildren = areaId != null &&
                getParentChildMap().containsKey(areaId) &&
                !getParentChildMap().get(areaId).isEmpty();

        area.setTreeLeaf(hasChildren ? "0" : "1"); // 0表示非叶子节点，1表示叶子节点
    }

    /**
     * 批量设置叶子状态
     */
    private void setTreeLeafForList(List<InfoArea> areas) {
        if (areas == null || areas.isEmpty()) {
            return;
        }

        Map<Integer, List<InfoArea>> parentChildMap = getParentChildMap();
        areas.forEach(area -> {
            Integer areaId = safeParseId(area.getId());
            boolean hasChildren = areaId != null &&
                    parentChildMap.containsKey(areaId) &&
                    !parentChildMap.get(areaId).isEmpty();
            area.setTreeLeaf(hasChildren ? "0" : "1");
        });
    }

    /**
     * 获取区域信息树（默认获取下两级）
     */
    public List<InfoArea> buildTree(String areaCode) {
        return buildTree(areaCode, 2);
    }

    /**
     * 根据上级地区编码查询出下级所有地区的编码（递归获取所有层级）
     */
    public List<String> getAllChildAreaCodes(String parentAreaCode) {
        if (!StringUtils.hasText(parentAreaCode)) {
            log.debug("父级区域代码为空");
            return Collections.emptyList();
        }

        try {
            InfoArea parentArea = getAreaCodeMap().get(parentAreaCode);
            if (parentArea == null) {
                log.warn("未找到父级区域，areaCode: {}", parentAreaCode);
                return Collections.emptyList();
            }

            Set<String> childCodes = new HashSet<>();
            collectChildAreaCodes(parentArea, childCodes, new HashSet<>());

            List<String> result = new ArrayList<>(childCodes);
            return result;
        } catch (Exception e) {
            log.error("获取子区域代码失败，parentAreaCode: {}", parentAreaCode, e);
            return Collections.emptyList();
        }
    }

    /**
     * 递归收集子区域代码
     */
    private void collectChildAreaCodes(InfoArea parentArea, Set<String> childCodes, Set<Integer> visited) {
        Integer parentId = safeParseId(parentArea.getId());
        if (parentId == null || visited.contains(parentId)) {
            return;
        }

        visited.add(parentId);
        List<InfoArea> children = getParentChildMap().getOrDefault(parentId, Collections.emptyList());

        for (InfoArea child : children) {
            if (StringUtils.hasText(child.getAreaCode())) {
                childCodes.add(child.getAreaCode());
                collectChildAreaCodes(child, childCodes, visited);
            }
        }
    }

    /**
     * 构建传入地区编码到下级地区编码的映射
     */
    public Map<String, String> buildAreaCodeToFirstLevelMap(String parentAreaCode, List<String> firstLevelAreaCodes, List<String> allChildAreaCodes) {
        Map<String, String> mapping = new HashMap<>();

        // 如果没有下级地区，直接映射到父地区
        if (firstLevelAreaCodes.isEmpty()) {
            for (String childCode : allChildAreaCodes) {
                mapping.put(childCode, parentAreaCode);
            }
            return mapping;
        }

        // 为每个下级地区查询其下属的所有地区
        for (String firstLevelCode : firstLevelAreaCodes) {
            List<String> subAreaCodes = this.getAllChildAreaCodes(firstLevelCode);
            // 下级地区本身
            mapping.put(firstLevelCode, firstLevelCode);
            // 下级地区的所有下属地区
            for (String subCode : subAreaCodes) {
                mapping.put(subCode, firstLevelCode);
            }
        }

        return mapping;
    }

    /**
     * 根据区域代码获取区域信息
     */
    public InfoArea getByAreaCode(String areaCode) {
        if (!StringUtils.hasText(areaCode)) {
            return null;
        }
        return getAreaCodeMap().get(areaCode);
    }

    /**
     * 批量根据区域代码获取区域信息
     */
    public List<InfoArea> getByAreaCodes(List<String> areaCodes) {
        if (areaCodes == null || areaCodes.isEmpty()) {
            return Collections.emptyList();
        }

        Map<String, InfoArea> areaCodeMap = getAreaCodeMap();
        return areaCodes.stream()
                .filter(StringUtils::hasText)
                .map(areaCodeMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 检查区域代码是否存在
     */
    public boolean existsByAreaCode(String areaCode) {
        return StringUtils.hasText(areaCode) && getAreaCodeMap().containsKey(areaCode);
    }

    /**
     * 获取区域层级
     */
    public Integer getAreaLevel(String areaCode) {
        InfoArea area = getByAreaCode(areaCode);
        return area != null ? area.getTreeLevel() : null;
    }

    /**
     * 根据父级ID获取直属子区域的区域代码列表
     */
    public List<String> getAreaCodesByParentId(String parentAreaCode) {
        if (!StringUtils.hasText(parentAreaCode)) {
            log.debug("父级区域代码为空");
            return Collections.emptyList();
        }

        try {
            InfoArea parentArea = getAreaCodeMap().get(parentAreaCode);
            if (parentArea == null) {
                log.warn("未找到父级区域，areaCode: {}", parentAreaCode);
                return Collections.emptyList();
            }

            Integer parentId = safeParseId(parentArea.getId());
            if (parentId == null) {
                return Collections.emptyList();
            }

            // 获取直属子区域（不递归）
            List<InfoArea> directChildren = getParentChildMap().getOrDefault(parentId, Collections.emptyList());

            List<String> childAreaCodes = directChildren.stream()
                    .map(InfoArea::getAreaCode)
                    .filter(StringUtils::hasText)
                    .collect(Collectors.toList());

            log.debug("区域 {} 的直属子区域代码数量: {}", parentAreaCode, childAreaCodes.size());
            return childAreaCodes;
        } catch (Exception e) {
            log.error("获取直属子区域代码失败，parentAreaCode: {}", parentAreaCode, e);
            return Collections.emptyList();
        }
    }

    /**
     * 随机获取一个直属子区域代码，如果没有子区域则返回父级本身
     */
    public String getRandomChildAreaCode(String parentAreaCode) {
        if (!StringUtils.hasText(parentAreaCode)) {
            log.debug("父级区域代码为空");
            return null;
        }

        try {
            // 获取直属子区域代码列表
            List<String> childAreaCodes = getAreaCodesByParentId(parentAreaCode);

            if (childAreaCodes.isEmpty()) {
                // 没有子区域，返回父级本身
                log.debug("区域 {} 没有直属子区域，返回父级本身", parentAreaCode);
                return parentAreaCode;
            }

            // 随机选择一个子区域
            Random random = new Random();
            String selectedAreaCode = childAreaCodes.get(random.nextInt(childAreaCodes.size()));
            log.debug("从区域 {} 的 {} 个子区域中随机选择了: {}", parentAreaCode, childAreaCodes.size(), selectedAreaCode);

            return selectedAreaCode;
        } catch (Exception e) {
            log.error("随机获取子区域代码失败，parentAreaCode: {}", parentAreaCode, e);
            return parentAreaCode; // 异常情况下返回父级
        }
    }
}
