package com.hxdi.nmjl.trash.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 车辆
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("INFO_CAR")
public class InfoCar extends DataEntity<InfoCar> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("ENTERPRISE_ID")
    private Integer enterpriseId;

    @TableField("ENTERPRISE_NAME")
    private String enterpriseName;

    @TableField("CODER")
    private String coder;

    @TableField("TYPER")
    private String typer;

    @TableField("LOADER")
    private String loader;

    @TableField("ENERGY")
    private String energy;

    @TableField("BRAND")
    private String brand;

    @TableField("MANUFACTURER")
    private String manufacturer;

    @TableField("HOME_LOCATION")
    private String homeLocation;

    @TableField("HOME_WAREHOUSE")
    private String homeWarehouse;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;


}
