package com.hxdi.nmjl.trash.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 货位
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("INFO_STORE_POSITION")
public class InfoStorePosition extends DataEntity<InfoStorePosition> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("HOUSE_ID")
    private Integer houseId;

    @TableField("GRANARY_ID")
    private Integer granaryId;

    @TableField("NAME")
    private String name;

    @TableField("CODER")
    private String coder;

    @TableField("CAPACITY")
    private BigDecimal capacity;

    @TableField("USE_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date useDate;

    @TableField("HOUSE_STATUS")
    private String houseStatus;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;
}
