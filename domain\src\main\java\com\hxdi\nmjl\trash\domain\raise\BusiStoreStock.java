package com.hxdi.nmjl.trash.domain.raise;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 库存
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("BUSI_STORE_STOCK")
@Deprecated
public class BusiStoreStock extends DataEntity<BusiStoreStock> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("CONTRACT_ID")
    private Integer contractId;

    @TableField("BATCH")
    private String batch;

    @TableField("STORE_ID")
    private Integer storeId;

    @TableField("STORE_NAME")
    private String storeName;

    @TableField("HOUSE_ID")
    private Integer houseId;

    @TableField("HOUSE_NAME")
    private String houseName;

    @TableField("OIL_TANK_ID")
    private Integer oilTankId;

    @TableField("OIL_TANK_NAME")
    private String oilTankName;

    @TableField("GRANARY_ID")
    private Integer granaryId;

    @TableField("GRANARY_NAME")
    private String granaryName;

    @TableField("POSITION_ID")
    private Integer positionId;

    @TableField("POSITION_NAME")
    private String positionName;

    @TableField("POSITION_CAPACITY")
    private BigDecimal positionCapacity;

    @TableField("ROOT_FOOD_CATEGORY_ID")
    private String rootFoodCategoryId;

    @TableField("ROOT_FOOD_CATEGORY_NAME")
    private String rootFoodCategoryName;

    @TableField("FOOD_CATEGORY_ID")
    private String foodCategoryId;

    @TableField("FOOD_CATEGORY_CODE")
    private String foodCategoryCode;

    @TableField("FOOD_CATEGORY_NAME")
    private String foodCategoryName;

    @TableField("TYPER")
    private String typer;

    @TableField("NAME")
    private String name;

    @TableField("PACKAGE_SPACE")
    private String packageSpace;

    @TableField("QUALITY_LEVEL")
    private String qualityLevel;

    @TableField("YEAR")
    private String year;

    @TableField("PRODUCE_PLACE")
    private String producePlace;

    @TableField("PROVINCE_ID")
    private Integer provinceId;

    @TableField("PROVINCE")
    private String province;

    @TableField("CITY_ID")
    private Integer cityId;

    @TableField("CITY")
    private String city;

    @TableField("COUNTY_ID")
    private Integer countyId;

    @TableField("COUNTY")
    private String county;

    @TableField("BEGIN_STOCK")
    private BigDecimal beginStock;

    @TableField("STOCK_IN")
    private BigDecimal stockIn;

    @TableField("STOCK_OUT")
    private BigDecimal stockOut;

    @TableField("END_STOCK")
    private BigDecimal endStock;

    @TableField("PRODUCTION_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date productionDate;

    @TableField("QUANTITY")
    private BigDecimal quantity;

    @TableField("FILLED_BY")
    private String filledBy;

    @TableField("FILLED_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date filledDate;

    @TableField("FILLED_CONTACT")
    private String filledContact;

    @TableField("STATION_ID")
    private Integer stationId;

    @TableField("STATION_NAME")
    private String stationName;

    @TableField("STATION_LONGITUDE")
    private BigDecimal stationLongitude;

    @TableField("STATION_LATITUDE")
    private BigDecimal stationLatitude;

    @TableField("MANAGEMENT_ID")
    private Integer managementId;

    @TableField("MANAGEMENT_NAME")
    private String managementName;

    @TableField("REMARKS")
    private String remarks;

    @TableField("UPLOAD_STATUS")
    private String uploadStatus;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;

    @TableField("BRAND")
    private String brand;
    @TableField("MODEL")
    private String model;
    @TableField("FOODSTUFF_LEVEL")
    private String foodstuffLevel;
    @TableField("SUPPLIER_ID")
    private String supplierId;
    @TableField("DELIVERY_QTY")
    private String deliveryQty;
    @TableField("RECEIPT_QTY")
    private String receiptQty;
    @TableField("INVENTORY_QTY")
    private String inventoryQty;
    @TableField("PRICE")
    private String price;
    @TableField("WARHOUSE_NAME")
    private String warhouseName;
}
