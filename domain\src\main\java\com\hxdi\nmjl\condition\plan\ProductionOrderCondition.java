package com.hxdi.nmjl.condition.plan;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * @program: nmjl-service
 * @description: 生产订单查询条件
 * @author: 王贝强
 * @create: 2025-07-16 13:47
 */
@Getter
@Setter
@ApiModel(description = "生产订单查询条件")
public class ProductionOrderCondition extends QueryCondition {

    @ApiModelProperty(value = "模糊查询：订单编号、生产批次号")
    private String search;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "库点id")
    private String storeId;

    @ApiModelProperty(value = "库点名称")
    private String storeName;

    @ApiModelProperty(value = "客户id")
    private String clientId;

    @ApiModelProperty(value = "客户名称")
    private String clientName;

    @ApiModelProperty(value = "订单状态：0-待开始，1-进行中，2-已检验，3-已交付")
    private Object state;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;


}
