package com.hxdi.nmjl.trash.domain.quality;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 质量标准-检验能力
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("BUSI_QUALITY_STANDARD_ABILITY")
public class BusiQualityStandardAbility extends DataEntity<BusiQualityStandardAbility> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("FOOD_CATEGORY_ID")
    private String foodCategoryId;

    @TableField("PROJECT_NAME")
    private String projectName;

    @TableField("BASIS")
    private String basis;

    @TableField("UNIT")
    private String unit;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;
}
