package com.hxdi.nmjl.trash.domain.raise;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 合同
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("BUSI_RAISE_CONTRACT")
public class BusiRaiseContract extends DataEntity<BusiRaiseContract> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("ROOT_FOOD_CATEGORY_ID")
    private String rootFoodCategoryId;

    @TableField("ROOT_FOOD_CATEGORY_NAME")
    private String rootFoodCategoryName;

    @TableField("FOOD_CATEGORY_ID")
    private String foodCategoryId;

    @TableField("FOOD_CATEGORY_CODE")
    private String foodCategoryCode;

    @TableField("FOOD_CATEGORY_NAME")
    private String foodCategoryName;

    @TableField("BRAND_ID")
    private String brandId;

    @TableField("BRAND_NAME")
    private String brandName;

    @TableField("BID_ID")
    private Integer bidId;

    @TableField("BID_CODER")
    private String bidCoder;

    @TableField("NAME")
    private String name;

    @TableField("CODER")
    private String coder;

    @TableField("TYPER")
    private String typer;

    @TableField("PURCHASE_ID")
    private Integer purchaseId;

    @TableField("PURCHASE_NAME")
    private String purchaseName;

    @TableField("START_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    @TableField("END_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;

    @TableField("OFFICE_ID")
    private Integer officeId;

    @TableField("OFFICE_CODE")
    private String officeCode;

    @TableField("OFFICE_NAME")
    private String officeName;

    @TableField("SUPPLIER_NAME")
    private String supplierName;

    @TableField("SUPPLIER_CODE")
    private String supplierCode;

    @TableField("SIGN_NAME")
    private String signName;

    @TableField("SIGN_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date signDate;

    @TableField("PAY_WAY")
    private String payWay;

    @TableField("CONTRACT_NUM")
    private BigDecimal contractNum;

    @TableField("CONTRACT_PRICE")
    private BigDecimal contractPrice;

    @TableField("CONTRACT_TOTAL")
    private BigDecimal contractTotal;

    @TableField("QUALITY_LEVEL")
    private String qualityLevel;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;
}
