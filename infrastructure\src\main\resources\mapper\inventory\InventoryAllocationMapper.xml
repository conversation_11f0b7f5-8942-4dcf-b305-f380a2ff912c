<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.inventory.InventoryAllocationMapper">

    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.inventory.InventoryAllocation">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="allocateCode" column="ALLOCATE_CODE" jdbcType="VARCHAR"/>
            <result property="outStoreId" column="OUT_STORE_ID" jdbcType="VARCHAR"/>
            <result property="outStoreName" column="OUT_STORE_NAME" jdbcType="VARCHAR"/>
            <result property="inStoreId" column="IN_STORE_ID" jdbcType="VARCHAR"/>
            <result property="inStoreName" column="IN_STORE_NAME" jdbcType="VARCHAR"/>
            <result property="allocateTime" column="ALLOCATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="allocateState" column="ALLOCATE_STATE" jdbcType="INTEGER"/>
            <result property="approveStatus" column="APPROVE_STATUS" jdbcType="INTEGER"/>
            <result property="approver" column="APPROVER" jdbcType="VARCHAR"/>
            <result property="approveTime" column="APPROVE_TIME" jdbcType="TIMESTAMP"/>
            <result property="approveOpinion" column="APPROVE_OPINION" jdbcType="VARCHAR"/>
            <result property="notes" column="NOTES" jdbcType="VARCHAR"/>
            <result property="attachment" column="ATTACHMENT" jdbcType="VARCHAR"/>
            <result property="enabled" column="ENABLED" jdbcType="INTEGER"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="createId" column="CREATE_ID" jdbcType="VARCHAR"/>
            <result property="updateId" column="UPDATE_ID" jdbcType="VARCHAR"/>
            <result property="tenantId" column="TENANT_ID" jdbcType="VARCHAR"/>
            <result property="dataHierarchyId" column="DATA_HIERARCHY_ID" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,ALLOCATE_CODE,OUT_STORE_ID,
        OUT_STORE_NAME,IN_STORE_ID,IN_STORE_NAME,
        ALLOCATE_TIME,ALLOCATE_STATE,APPROVE_STATUS,
        APPROVER,APPROVE_TIME,APPROVE_OPINION,
        NOTES,ATTACHMENT,ENABLED,
        CREATE_TIME,UPDATE_TIME,CREATE_ID,
        UPDATE_ID,TENANT_ID,DATA_HIERARCHY_ID
    </sql>


    <select id="listV1" parameterType="com.hxdi.nmjl.condition.inventory.InventoryAllocationCondition" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from B_INVENTORY_ALLOCATION
        <where>
            <if test="condition.status != null">
                and ALLOCATE_STATE = #{condition.status}
            </if>
            <if test="condition.startTime != null">
                and ALLOCATE_TIME &gt;= #{condition.startTime}
            </if>
            <if test="condition.endTime != null">
                and ALLOCATE_TIME &lt;= #{condition.endTime}
            </if>
            and ENABLED = 1
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <select id="PageV1" parameterType="com.hxdi.nmjl.condition.inventory.InventoryAllocationCondition" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include>
            from B_INVENTORY_ALLOCATION
        <where>
            <if test="condition.status != null">
                and ALLOCATE_STATE = #{condition.status}
            </if>
            <if test="condition.startTime != null">
                and ALLOCATE_TIME &gt;= #{condition.startTime}
            </if>
            <if test="condition.endTime != null">
                and ALLOCATE_TIME &lt;= #{condition.endTime}
            </if>
        </where>
    </select>
</mapper>
