package com.hxdi.nmjl.condition.inventory;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel(description="库存记录查询条件")
public class InventoryAllocationCondition extends QueryCondition {
    @ApiModelProperty(value="开始时间")
    private String startTime;

    @ApiModelProperty(value="结束时间")
    private String endTime;

    @ApiModelProperty(value="执行状态")
    private String status;

    @ApiModelProperty(value="任务单号")
    private String taskCode;
}