package com.hxdi.nmjl.service.specialproduct;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.specialproduct.SpecialProductMonitoring;
import com.hxdi.nmjl.vo.condition.specialproduct.SpecialProductMonitoringCondition;

import java.util.List;

/**
 * 优特产品分析报告服务接口
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/16
 */
public interface SpecialProductMonitoringService extends IBaseService<SpecialProductMonitoring> {

    /**
     * 创建分析报告
     * @param monitoring 分析报告实体
     */
    void create(SpecialProductMonitoring monitoring);

    /**
     * 更新分析报告
     * @param monitoring 分析报告实体
     */
    void update(SpecialProductMonitoring monitoring);

    /**
     * 删除分析报告
     * @param id 报告ID
     */
    void remove(String id);

    /**
     * 获取报告详情
     * @param id 报告ID
     * @return 分析报告实体
     */
    SpecialProductMonitoring getDetail(String id);

    /**
     * 分页查询分析报告
     * @param condition 查询条件
     * @return 分页结果
     */
    Page<SpecialProductMonitoring> pages(SpecialProductMonitoringCondition condition);

    /**
     * 列表查询分析报告
     * @param condition 查询条件
     * @return 分析报告列表
     */
    List<SpecialProductMonitoring> lists(SpecialProductMonitoringCondition condition);


}
