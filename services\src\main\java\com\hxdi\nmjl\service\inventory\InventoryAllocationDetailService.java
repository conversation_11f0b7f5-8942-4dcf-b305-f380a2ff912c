package com.hxdi.nmjl.service.inventory;

import com.hxdi.nmjl.domain.inventory.InventoryAllocationDetail;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【B_INVENTORY_ALLOCATION_DETAIL(库存调配明细)】的数据库操作Service
* @createDate 2025-08-01 10:28:22
*/
public interface InventoryAllocationDetailService extends IService<InventoryAllocationDetail> {
    void removeV1(String id);

    List<InventoryAllocationDetail>getListV1(String id);
}
