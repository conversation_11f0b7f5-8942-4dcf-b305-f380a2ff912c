package com.hxdi.nmjl.domain.mobilization;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;


/**
 * 厨师动员信息
 */
@ApiModel(description = "厨师动员信息")
@Getter
@Setter
@TableName("B_MOBILIZED_CHEF_INFO") // 表名映射
public class MobilizeChefInfo implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    @TableField("NAME")
    @ApiModelProperty(value = "姓名")
    private String name;

    @TableField("ID_CARD")
    @ApiModelProperty(value = "身份证号")
    private String idCard;

    @TableField("BIRTHDAY")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "出生日期")
    private String birthDay;

    @TableField("GENDER")
    @ApiModelProperty(value = "性别")
    private String gender;

    @TableField("POLITICS_STATUS")
    @ApiModelProperty(value = "政治面貌 1-团员，2-党员、3-群众、4-其它")
    private Integer politicsStatus;

    @TableField("MOBILE")
    @ApiModelProperty(value = "本人联系电话")
    private String mobile;

    @TableField("CONTACT1")
    @ApiModelProperty(value = "联系人1姓名")
    private String contact1;

    @TableField("CONTACT_REF1")
    @ApiModelProperty(value = "联系人1关系")
    private Integer contactRef1;

    @TableField("CONTRACT_TEL1")
    @ApiModelProperty(value = "联系人1电话")
    private String contractTel1;

    @TableField("CONTACT2")
    @ApiModelProperty(value = "联系人2姓名")
    private String contact2;

    @TableField("CONTACT_REF2")
    @ApiModelProperty(value = "联系人2关系")
    private Integer contactRef2;

    @TableField("CONTRACT_TEL2")
    @ApiModelProperty(value = "联系人2电话")
    private String contractTel2;

    @TableField("CONTACT3")
    @ApiModelProperty(value = "联系人3姓名")
    private String contact3;

    @TableField("CONTACT_REF3")
    @ApiModelProperty(value = "联系人3关系")
    private Integer contactRef3;

    @TableField("CONTRACT_TEL3")
    @ApiModelProperty(value = "联系人3电话")
    private String contractTel3;

    @TableField("PROFILE")
    @ApiModelProperty(value = "个人简介")
    private String profile;

    // 能力标签字段
    @TableField("TAGS")
    @ApiModelProperty(hidden = true) // 隐藏实际存储字段，不暴露给前端
    private String tags; // 数据库实际存储的字符串（逗号分隔）

    @TableField(exist = false) // 标记为非数据库字段
    @ApiModelProperty(value = "能力标签列表")
    private List<String> tagsStr; // 业务层使用的标签列表

    @TableField("CHEF_CERT_FILE")
    @ApiModelProperty(hidden = true)
    private String chefCertFile;

    @TableField(exist = false)
    @ApiModelProperty(value = "厨师证")
    private List<String> chefCertFileStr;

    @TableField("IDCARD_FILE")
    @ApiModelProperty(value = "身份证件附件")
    private String idcardFile;

    @TableField("APPROVE_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "审核时间")
    private Date approveTime;

    @TableField("APPROVE_STATUS")
    @ApiModelProperty(value = "0:未提交、1:已提交、2:审批通过、3.审批未通过")
    private Integer approveStatus;

    @TableField("APPROVER")
    @ApiModelProperty(value = "审批人")
    private String approver;

    @TableField("APPROVE_OPINION")
    @ApiModelProperty(value = "审核意见")
    private String approveOpinion;

    @TableField("ATTACHMENT")
    @ApiModelProperty(value = "附件")
    private String attachment;

    @TableField("ENABLED")
    @ApiModelProperty(value = "数据状态")
    private Integer enabled;

    @TableField("ATTACHEMENTS")
    @ApiModelProperty(value = "其他附件")
    private String attachements;

    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建id")
    private String createId;

    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新id")
    private String updateId;

    @TableField("TENANT_ID")
    @ApiModelProperty(value = "租户id")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    @ApiModelProperty(value = "所属组织")
    private String dataHierarchyId;

    // 厨师经历详情
    @TableField(exist = false)
    private MobilizeChefExp mobilizeChefExp;

    /*// 标签转换函数（tags 和 tagsStr 逻辑互换）
    public void setTags(String tags) {
        this.tags = tags;
        // 字符串转列表："川菜,粤菜" → ["川菜","粤菜"]
        if (tags != null && !tags.isEmpty()) {
            this.tagsStr = Arrays.asList(tags.split(","));
        } else {
            this.tagsStr = new ArrayList<>(); // 避免 null
        }
    }

    public String getTags() {
        // 列表转字符串：["川菜","粤菜"] → "川菜,粤菜"
        if (tagsStr != null && !tagsStr.isEmpty()) {
            this.tags = String.join(",", tagsStr);
        } else {
            this.tags = null;
        }
        return this.tags;
    }

    public void setChefCertFile(String chefCertFile) {
        this.chefCertFile = chefCertFile;
        // 字符串转列表："file1.jpg,file2.jpg" → ["file1.jpg", "file2.jpg"]
        if (chefCertFile != null && !chefCertFile.isEmpty()) {
            this.chefCertFileStr = Arrays.asList(chefCertFile.split(","));
        } else {
            this.chefCertFileStr = new ArrayList<>();
        }
    }

    public String getChefCertFile() {
        // 列表转字符串：["file1.jpg", "file2.jpg"] → "file1.jpg,file2.jpg"
        if (chefCertFileStr != null && !chefCertFileStr.isEmpty()) {
            this.chefCertFile = String.join(",", chefCertFileStr);
        } else {
            this.chefCertFile = null;
        }
        return this.chefCertFile;
    }*/
}
