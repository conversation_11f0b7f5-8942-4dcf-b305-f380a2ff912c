package com.hxdi.nmjl.service.plan.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.model.DataType;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.condition.plan.SaleCondition;
import com.hxdi.nmjl.domain.inout.InoutDetail;
import com.hxdi.nmjl.domain.inout.InoutItem;
import com.hxdi.nmjl.domain.plan.SaleOrder;
import com.hxdi.nmjl.domain.plan.SaleOrderItem;
import com.hxdi.nmjl.feign.SystemNumberRuleClient;
import com.hxdi.nmjl.mapper.plan.SaleOrderMapper;
import com.hxdi.nmjl.service.bigscreen.OrderMonthRecordService;
import com.hxdi.nmjl.service.plan.ContractInfoService;
import com.hxdi.nmjl.service.plan.SaleOrderItemService;
import com.hxdi.nmjl.service.plan.SaleOrderService;
import com.hxdi.system.client.model.dto.BusinessCode;
import com.hxdi.system.client.model.dto.BusinessCodeParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <销售订单服务实现>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/25 14:20
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class SaleOrderServiceImpl extends BaseServiceImpl<SaleOrderMapper, SaleOrder> implements SaleOrderService {

    @Resource
    private SystemNumberRuleClient systemNumberRuleClient;

    @Resource
    private SaleOrderItemService saleOrderItemService;

    @Resource
    private ContractInfoService contractInfoService;

    @Resource
    private OrderMonthRecordService orderMonthRecordService;

    @Override
    public SaleOrder getDetail(String orderId) {
        SaleOrder saleOrder = baseMapper.selectById(orderId);
        saleOrder.setDetailList(saleOrderItemService.getList(orderId));
        return saleOrder;
    }

    @Override
    public Page<SaleOrder> pages(SaleCondition condition) {
        Page<SaleOrder> page = condition.newPage();
        baseMapper.selectPageV1(page, condition);
        // page.getRecords().forEach(item -> item.setDetailList(saleDetailService.getList(item.getId())));
        return page;
    }

    @Override
    public List<SaleOrder> lists(SaleCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public List<SaleOrder> listsV1(SaleCondition condition) {
        List<SaleOrder> orderList = baseMapper.selectListV1(condition);
        if (orderList.isEmpty()) {
            return Collections.emptyList();
        }
        List<String> idList = orderList.stream().map(SaleOrder::getId).collect(Collectors.toList());
        List<SaleOrderItem> itemList = saleOrderItemService.getList(idList);
        if (!itemList.isEmpty()) {
            Map<String, List<SaleOrderItem>> itemMap = itemList.stream().collect(Collectors.groupingBy(SaleOrderItem::getOrderId));
            orderList.forEach(item -> item.setDetailList(itemMap.getOrDefault(item.getId(), Collections.emptyList())));
        }
        return orderList;
    }

    public void createV1(SaleOrder saleOrder) {

        //生成订单编码
        BusinessCodeParams params = new BusinessCodeParams();
        params.setCode("SALE_ORDER_CODE");
        params.setDt(DataType.STRING);
        BusinessCode businessCode = systemNumberRuleClient.getNumber(params).getData();
        saleOrder.setOrderCode((String) businessCode.getValue());

        saleOrder.setState(1);
        //将数据权限设置为库点
        String dataHierarchyId = saleOrder.getStoreId();
        saleOrder.setDataHierarchyId(dataHierarchyId);

        this.save(saleOrder);
        saleOrder.getDetailList().forEach(detail -> {
            detail.setOrderId(saleOrder.getId());
            detail.setContractId(saleOrder.getContractId());
            detail.setDataHierarchyId(dataHierarchyId);
        });
        saleOrderItemService.saveBatch(saleOrder.getDetailList());
    }

    public void updateV1(SaleOrder saleOrder) {
        this.updateById(saleOrder);
        if (saleOrder.getDetailList() != null) {
            saleOrderItemService.removeV1(saleOrder.getId());
            saleOrder.getDetailList().forEach(detail -> {
                detail.setOrderId(saleOrder.getId());
                detail.setContractId(saleOrder.getContractId());
            });
            saleOrderItemService.saveBatch(saleOrder.getDetailList());
        }
    }

    @Override
    public void removeV1(String orderId) {
        //逻辑删除
        SaleOrder saleOrder = new SaleOrder();
        saleOrder.setId(orderId);
        saleOrder.setEnabled(0);
        this.updateById(saleOrder);
    }

    @Override
    public void submitV1(String orderId) {
        SaleOrder saleOrder = this.getById(orderId);
        if (saleOrder.getState() != 1) {
            throw new BaseException("当前订单已确认，无法再次提交！");
        }
        saleOrder.setState(2);
        this.updateById(saleOrder);
    }

    public void updateState(String id, Integer state) {
        SaleOrder saleOrder = this.getById(id);
        if (saleOrder.getState().equals(state)) {
            throw new BaseException("当前订单状态与目标状态一致，无需更新！");
        }
        saleOrder.setState(state);
        this.updateById(saleOrder);

        //已完成的订单，更新合同、标的、筹措计划的完成情况
        if (state == 6) {
            List<SaleOrderItem> itemList = saleOrderItemService.getList(id);
            contractInfoService.updateProcessV1(itemList);
            //todo 更新筹措计划的完成情况
        }
    }

    @Override
    public void updateProcess(String orderId, String catalogId, String grade, BigDecimal changeQty) {
        SaleOrder order = this.getById(orderId);
        if (order == null) {
            throw new BaseException("订单不存在");
        }
        SaleOrderItem item = saleOrderItemService.listByCatalogIdAndGrade(orderId, catalogId, grade);
        if (item == null) {
            throw new BaseException("订单明细不存在");
        }
        //更新完成数量
        BigDecimal currentQty = item.getCompletedQty() != null ? item.getCompletedQty() : BigDecimal.ZERO;
        item.setCompletedQty(currentQty.add(changeQty));

        //查询当前是否所有的品种都已完成
        List<SaleOrderItem> itemList = saleOrderItemService.getList(orderId);
        boolean allCompleted = itemList.stream().allMatch(detail -> detail.getCompletedQty().compareTo(detail.getOrderQty()) >= 0);
        if (allCompleted) {
            //更新订单状态为已完成
            this.updateState(orderId, 6);
        }

        saleOrderItemService.updateById(item);
    }

    @Override
    public void generateOrderStatistics(List<InoutDetail> inoutDetailList) {
        // 生成销售订单统计数据
        // 统计当天的所有订单的完成情况，并为每个订单生成一条统计数据
        
        if (inoutDetailList == null || inoutDetailList.isEmpty()) {
            log.warn("出入库明细数据为空，跳过统计生成");
            return;
        }
        
        // 先根据订单编号分组
        Map<String, List<InoutDetail>> orderMap = inoutDetailList.stream()
                .filter(detail -> detail.getOrderCode() != null && !detail.getOrderCode().trim().isEmpty())
                .collect(Collectors.groupingBy(InoutDetail::getOrderCode));
                
        orderMap.forEach((orderCode, detailList) -> {
            try {
                // 根据订单编号查询订单信息
                SaleCondition condition = new SaleCondition();
                condition.setSearch(orderCode);
                List<SaleOrder> orderList = this.lists(condition);
                
                if (orderList.isEmpty()) {
                    log.warn("未找到订单编号为 {} 的销售订单", orderCode);
                    return;
                }
                
                SaleOrder saleOrder = orderList.get(0);
                
                // 从出入库明细中统计完成数量
                BigDecimal totalCompletedQty = BigDecimal.ZERO;
                int totalItemCount = 0;
                
                for (InoutDetail detail : detailList) {
                    if (detail.getDetailList() != null && !detail.getDetailList().isEmpty()) {
                        for (InoutItem item : detail.getDetailList()) {
                            // 累计数量
                            if (item.getQty() != null) {
                                totalCompletedQty = totalCompletedQty.add(item.getQty());
                            }
                            totalItemCount++;
                        }
                    }
                }
                
                // 生成月度统计记录（其他信息如金额等从订单中获取）
                orderMonthRecordService.generateRecord("2",saleOrder.getId(),inoutDetailList.get(0).getDetailList().get(0).getCatalogId(),inoutDetailList.get(0).getDetailList().get(0).getGrade(),saleOrder.getDetailList().get(0).getReserveLevel(),
                        totalCompletedQty);
                        
                log.info("已生成订单 {} 的统计数据，完成数量: {}, 明细条数: {}", 
                        orderCode, totalCompletedQty, totalItemCount);
                        
            } catch (Exception e) {
                log.error("生成订单 {} 统计数据失败: {}", orderCode, e.getMessage(), e);
            }
        });
    }
}
