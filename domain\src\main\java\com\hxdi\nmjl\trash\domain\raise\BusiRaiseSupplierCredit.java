package com.hxdi.nmjl.trash.domain.raise;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 供应商-信用
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("BUSI_RAISE_SUPPLIER_CREDIT")
@Deprecated
public class BusiRaiseSupplierCredit extends DataEntity<BusiRaiseSupplierCredit> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("SUPPLIER_ID")
    private Integer supplierId;

    @TableField("CHECK_UNIT")
    private String checkUnit;

    @TableField("CHECK_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date checkTime;

    @TableField("PROBLEM")
    private String problem;

    @TableField("CHECK_MAN")
    private String checkMan;

    @TableField("OPINION")
    private String opinion;

    @TableField("PROBLEM_DESC")
    private String problemDesc;

    @TableField("HANDLED_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date handledTime;

    @TableField("IS_HANDLED")
    private String isHandled;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;
}
