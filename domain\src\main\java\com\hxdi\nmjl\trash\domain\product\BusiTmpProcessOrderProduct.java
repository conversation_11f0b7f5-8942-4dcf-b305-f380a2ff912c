package com.hxdi.nmjl.trash.domain.product;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 生产加工订单-商品
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("BUSI_TMP_PROCESS_ORDER_PRODUCT")
public class BusiTmpProcessOrderProduct extends DataEntity<BusiTmpProcessOrderProduct> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("PROCESS_ORDER_ID")
    private Integer processOrderId;

    @TableField("ROOT_FOOD_CATEGORY_ID")
    private String rootFoodCategoryId;

    @TableField("ROOT_FOOD_CATEGORY_NAME")
    private String rootFoodCategoryName;

    @TableField("FOOD_CATEGORY_ID")
    private String foodCategoryId;

    @TableField("FOOD_CATEGORY_CODE")
    private String foodCategoryCode;

    @TableField("FOOD_CATEGORY_NAME")
    private String foodCategoryName;

    @TableField("BRAND_ID")
    private String brandId;

    @TableField("BRAND_NAME")
    private String brandName;

    @TableField("SPEC")
    private String spec;

    @TableField("QUALITY_LEVEL")
    private String qualityLevel;

    @TableField("QUANTITY")
    private Integer quantity;

    @TableField("AMOUNT")
    private BigDecimal amount;

    @TableField("TOTAL_AMOUNT")
    private BigDecimal totalAmount;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;
}
