package com.hxdi.nmjl.service.mobilization;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.mobilization.MobilizeChefInfo;
import com.hxdi.nmjl.trash.dto.mobilize.MobilizeChefCondition;


import java.util.List;

/**
 * 厨师动员信息服务接口
 */
public interface MobilizeChefService extends IBaseService<MobilizeChefInfo> {

    /**
     * 查询厨师详细信息
     * @param id 厨师ID
     * @return MobilizeCook
     */
    MobilizeChefInfo getDetail(String id);

    /**
     * 分页查询厨师信息
     * @param condition 查询条件
     * @return Page<MobilizeCook>
     */
    Page<MobilizeChefInfo> getPages(MobilizeChefCondition condition);

    /**
     * 列表查询厨师信息
     * @param condition 查询条件
     * @return List<BusiMobilizeCook>
     */
    List<MobilizeChefInfo> getList(MobilizeChefCondition condition);

    /**
     * 新增厨师信息
     * @param cook 厨师信息
     * @return boolean
     */
    void add(MobilizeChefInfo cook);

    /**
     * 更新厨师信息
     * @param cook 厨师信息
     * @return boolean
     */
    void update(MobilizeChefInfo cook);

    /**
     * 删除厨师信息
     * @param id 厨师ID
     * @return boolean
     */
    boolean delete(String id);

    /**
            * 审核
     *
             * @param id   调整ID
     * @param approveOpinion 审批意见
     */
    void approve(String id, String approveOpinion);

    /**
     * 驳回
     *
     * @param id   调整ID
     * @param approveOpinion 审批意见
     */
    void reject(String id, String approveOpinion);

    /**
     * 提交
     *
     * @param id   调整ID
     */
    void submit(String id);


}
