package com.hxdi.nmjl.condition.plan;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import lombok.Getter;
import lombok.Setter;

@ApiModel(value = "招标信息查询条件")
@Getter
@Setter
public class BidInfoCondition extends QueryCondition {

    @ApiModelProperty(value = "关联计划ID")
    private String planId;

    @ApiModelProperty(value = "招标单位ID")
    private String unitId;

    @ApiModelProperty(value = "中标企业id")
    private String clientId;

    @ApiModelProperty(value = "模糊查询：项目名称或编号")
    private String search;

    @ApiModelProperty(value = "招标组织形式:1委托招标,2自行招标")
    private Integer bidType;

    @ApiModelProperty(value = "状态：0-未开始，1-进行中，2-已完成，3-流标")
    private Integer state;

    @ApiModelProperty(value = "审核状态：0-未审核，1-已审核，2-驳回")
    private String ApproveStatus;

    @ApiModelProperty(value="招标开始时间")
    private Date startTime;

    @ApiModelProperty(value="招标结束时间")
    private Date endTime;

}
