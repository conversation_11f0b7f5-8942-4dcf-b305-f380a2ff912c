package com.hxdi.nmjl.trash.dto.mobilize;

import com.baomidou.mybatisplus.annotation.TableField;
import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel(value = "厨师动员信息查询条件")
public class MobilizeChefCondition extends QueryCondition {

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "性别")
    private String gender;

    @ApiModelProperty(value = "政治面貌 1-团员，2-党员、3-群众、4-其它")
    private Integer politicsStatus;

    @TableField("APPROVE_STATUS")
    @ApiModelProperty(value = "0:未提交、1:已提交、2:审批通过、3.审批未通过")
    private Integer approveStatus;
}
