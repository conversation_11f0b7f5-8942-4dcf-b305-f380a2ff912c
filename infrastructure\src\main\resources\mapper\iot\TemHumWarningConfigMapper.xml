<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.iot.TemHumWarningConfigMapper">
    <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.plan.TemHumWarningConfig">
        <!--@mbg.generated-->
        <!--@Table B_TEM_HUM_WARNING_CONFIG-->
        <id column="ID" property="id"/>
        <result column="STORE_ID" property="storeId"/>
        <result column="STORE_NAME" property="storeName"/>
        <result column="ST_ID" property="stId"/>
        <result column="ST_NAME" property="stName"/>
        <result column="TASK_TYPE" property="taskType"/>
        <result column="INDEX_NAME" property="indexName"/>
        <result column="MAX_VALUE" property="maxValue"/>
        <result column="MIN_VALUE" property="minValue"/>
<!--        <result column="DEVICE_TYPE" property="deviceType"/>-->
<!--        <result column="MSG" property="msg"/>-->
<!--        <result column="PUSH_TYPE" property="pushType"/>-->
<!--        <result column="ROLES" property="roles"/>-->
        <result column="USERS" property="users"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="UPDATE_TIME" property="updateTime"/>
        <result column="CREATE_ID" property="createId"/>
        <result column="UPDATE_ID" property="updateId"/>
        <result column="TENANT_ID" property="tenantId"/>
        <result column="DATA_HIERARCHY_ID" property="dataHierarchyId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID,
        STORE_ID,
        STORE_NAME,
        ST_ID,
        ST_NAME,
        TASK_TYPE,
        INDEX_NAME,
        MAX_VALUE,
        MIN_VALUE,
<!--        DEVICE_TYPE,-->
<!--        MSG,-->
<!--        PUSH_TYPE,-->
<!--        ROLES,-->
        USERS,
        CREATE_TIME,
        UPDATE_TIME,
        CREATE_ID,
        UPDATE_ID,
        TENANT_ID,
        DATA_HIERARCHY_ID
    </sql>

    <select id="selectListV1" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM B_TEM_HUM_WARNING_CONFIG
        <where>
            <if test="@plugins.OGNL@isNotEmpty(condition.storeId)">
                AND STORE_ID = #{condition.storeId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.stId)">
                AND ST_ID = #{condition.stId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.indexName)">
                AND INDEX_NAME = #{condition.indexName}
            </if>
        </where>
        order by CREATE_TIME DESC
    </select>

    <select id="selectPageV1" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM B_TEM_HUM_WARNING_CONFIG
        <where>
            <if test="@plugins.OGNL@isNotEmpty(condition.storeId)">
                AND STORE_ID = #{condition.storeId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.stId)">
                AND ST_ID = #{condition.stId}
            </if>
            <if test="@plugins.OGNL@isNotEmpty(condition.indexName)">
                AND INDEX_NAME = #{condition.indexName}
            </if>
        </where>
        order by CREATE_TIME DESC
    </select>
</mapper>