package com.hxdi.nmjl.enums;

import lombok.Getter;

/**
 * @program: nmjl-service
 * @description: 温湿度检测指标项枚举
 * @author: 王贝强
 * @create: 2025-07-21 19:54
 */
@Getter
public enum TemHumSymbol {
    TEM("仓温","cw","℃"),
    HUM("仓湿","cs","%"),
    AIR_TEM("气温","qw","℃"),
    AIR_HUM("气湿","qs","%");

    private final String name;
    private final String label;
    private final String symbol;

    TemHumSymbol(String name, String label, String symbol) {
        this.name = name;
        this.label = label;
        this.symbol = symbol;
    }

    public static TemHumSymbol getByLabel(String label) {
        for (TemHumSymbol temHumSymbol : TemHumSymbol.values()) {
            if (temHumSymbol.getLabel().equals(label)) {
                return temHumSymbol;
            }
        }
        return null;
    }
}
