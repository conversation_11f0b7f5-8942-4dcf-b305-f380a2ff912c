package com.hxdi.nmjl.trash.domain.supply;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 客户
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("BUSI_SUPPLY_CUSTOMER")
public class BusiSupplyCustomer extends DataEntity<BusiSupplyCustomer> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("MANAGEMENT_ID")
    private Integer managementId;

    @TableField("MANAGEMENT_NAME")
    private String managementName;

    @TableField("STATION_ID")
    private Integer stationId;

    @TableField("STATION_NAME")
    private String stationName;

    @TableField("USER_ID")
    private Integer userId;

    @TableField("CONTACT_BY")
    private String contactBy;

    @TableField("PHONE")
    private String phone;

    @TableField("CUSTOMER_NAME")
    private String customerName;

    @TableField("PROVINCE_ID")
    private Integer provinceId;

    @TableField("PROVINCE")
    private String province;

    @TableField("CITY_ID")
    private Integer cityId;

    @TableField("CITY")
    private String city;

    @TableField("COUNTY_ID")
    private Integer countyId;

    @TableField("COUNTY")
    private String county;

    @TableField("ADDRESS")
    private String address;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;

    @TableField("LEGAL_PERSON")
    private String legalPerson;

    @TableField("CODE")
    private String code;

    @TableField("CREDIT_CODE")
    private String creditCode;

    @TableField("COMPANY_TYPE")
    private String companyType;

}
