package com.hxdi.nmjl.trash.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 粮食品种
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("INFO_FOOD_CATEGORY")
public class InfoFoodCategory extends DataEntity<InfoFoodCategory> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("PID")
    private Integer pid;

    @TableField("NAME")
    private String name;

    @TableField("NICK_NAME")
    private String nickName;

    @TableField("CODER_GB")
    private String coderGb;

    @TableField("CODER_IB")
    private String coderIb;

    @TableField("CODER_CB")
    private String coderCb;

    @TableField("USE_TYPE")
    private String useType;

    @TableField("REMARKS")
    private String remarks;

    @TableField("MAX_STORAGE_YEAR")
    private Integer maxStorageYear;

    @TableField("GROUP_MARK")
    private String groupMark;

    @TableField("TREE_SORT")
    private Integer treeSort;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;
}
