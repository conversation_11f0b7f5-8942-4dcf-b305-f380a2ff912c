package com.hxdi.nmjl.condition.quality;

import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * @program: nmjl-service
 * @description: 质检检验信息查询条件
 * @author: 王贝强
 * @create: 2025-04-18 16:25
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "质检检验信息查询条件")
public class QualityInspectionCondition extends QueryCondition {

    @ApiModelProperty(value="检验编号")
    private String inspectionNo;

    @ApiModelProperty(value="检验名称")
    private String name;

    @ApiModelProperty(value="检验报告类型:字典JYBGLX")
    private String inspectType;

    @ApiModelProperty(value="库点ID")
    private String storeId;

    @ApiModelProperty(value="生产批次")
    private String batchNum;

    @ApiModelProperty(value="品种ID")
    private String catalogId;

    @ApiModelProperty(value="品种名称")
    private String catalogName;

    @ApiModelProperty(value="检测日期区间开始")
    private Date inspectTimeStart;

    @ApiModelProperty(value="检测日期区间结束")
    private Date inspectTimeEnd;

}
