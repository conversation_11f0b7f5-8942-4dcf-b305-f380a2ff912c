package com.hxdi.nmjl.trash.domain.quality;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("BUSI_QUALITY_MECHANISM")
public class BusiQualityMechanism extends DataEntity<BusiQualityMechanism> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("CODE")
    private String code;

    @TableField("NAME")
    private String name;

    @TableField("NATURE")
    private String nature;

    @TableField("PROVINCE_ID")
    private Integer provinceId;

    @TableField("PROVINCE")
    private String province;

    @TableField("CITY_ID")
    private Integer cityId;

    @TableField("CITY")
    private String city;

    @TableField("COUNTY_ID")
    private Integer countyId;

    @TableField("COUNTY")
    private String county;

    @TableField("FIELD")
    private String field;

    @TableField("LEGAL_PERSON")
    private String legalPerson;

    @TableField("PHONE")
    private String phone;

    @TableField("FAX")
    private String fax;

    @TableField("EMAIL")
    private String email;

    @TableField("ADDRESS")
    private String address;

    @TableField("ZIP_CODE")
    private String zipCode;

    @TableField("CONTACT")
    private String contact;

    @TableField("DEPARTMENT")
    private String department;

    @TableField("LEVEL")
    private String level;

    @TableField("EMP_COUNT")
    private Integer empCount;

    @TableField("HIGH_COUNT")
    private Integer highCount;

    @TableField("MIDDLE_COUNT")
    private Integer middleCount;

    @TableField("PRIMARY_COUNT")
    private Integer primaryCount;

    @TableField("DOCTOR_COUNT")
    private Integer doctorCount;

    @TableField("MASTER_COUNT")
    private Integer masterCount;

    @TableField("BACHELOR_COUNT")
    private Integer bachelorCount;

    @TableField("JUNIOR_COLLEGE_COUNT")
    private Integer juniorCollegeCount;

    @TableField("TOTAL_ASSET")
    private String totalAsset;

    @TableField("AREA")
    private String area;

    @TableField("ASSET_NATURE")
    private String assetNature;

    @TableField("CHECK_COUNT")
    private Integer checkCount;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;
}
