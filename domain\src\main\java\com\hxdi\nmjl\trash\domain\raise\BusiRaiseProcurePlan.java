package com.hxdi.nmjl.trash.domain.raise;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 采购需求计划
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("BUSI_RAISE_PROCURE_PLAN")
public class BusiRaiseProcurePlan extends DataEntity<BusiRaiseProcurePlan> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("ROOT_FOOD_CATEGORY_ID")
    private String rootFoodCategoryId;

    @TableField("ROOT_FOOD_CATEGORY_NAME")
    private String rootFoodCategoryName;

    @TableField("FOOD_CATEGORY_ID")
    private String foodCategoryId;

    @TableField("FOOD_CATEGORY_CODE")
    private String foodCategoryCode;

    @TableField("FOOD_CATEGORY_NAME")
    private String foodCategoryName;

    @TableField("YEAR")
    private String year;

    @TableField("STATION_ID")
    private Integer stationId;

    @TableField("STATION_NAME")
    private String stationName;

    @TableField("PROVINCE_ID")
    private Integer provinceId;

    @TableField("PROVINCE")
    private String province;

    @TableField("CITY_ID")
    private Integer cityId;

    @TableField("CITY")
    private String city;

    @TableField("COUNTY_ID")
    private Integer countyId;

    @TableField("COUNTY")
    private String county;

    @TableField("DISTRIBUTION_PROVINCE_ID")
    private Integer distributionProvinceId;

    @TableField("DISTRIBUTION_PROVINCE")
    private String distributionProvince;

    @TableField("DISTRIBUTION_CITY_ID")
    private Integer distributionCityId;

    @TableField("DISTRIBUTION_CITY")
    private String distributionCity;

    @TableField("DISTRIBUTION_COUNTY_ID")
    private Integer distributionCountyId;

    @TableField("DISTRIBUTION_COUNTY")
    private String distributionCounty;

    @TableField("MIN_QUANTITY")
    private BigDecimal minQuantity;

    @TableField("MAX_QUANTITY")
    private BigDecimal maxQuantity;

    @TableField("PACKAGE_SPEC")
    private String packageSpec;

    @TableField("FREQUENCY")
    private String frequency;

    @TableField("REPORT_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date reportDate;

    @TableField("REPORT_NAME")
    private String reportName;

    @TableField("CHECK_STATUS")
    private String checkStatus;

    @TableField("CHECKED_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date checkedTime;

    @TableField("UPDATE_BY")
    private String updateBy;

    @TableField("UPDATE_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;
}
