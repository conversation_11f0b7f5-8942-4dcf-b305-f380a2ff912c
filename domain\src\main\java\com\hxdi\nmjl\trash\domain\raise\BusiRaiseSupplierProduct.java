package com.hxdi.nmjl.trash.domain.raise;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 供应商产品
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("BUSI_RAISE_SUPPLIER_PRODUCT")
@Deprecated
public class BusiRaiseSupplierProduct extends DataEntity<BusiRaiseSupplierProduct> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("SUPPLIER_ID")
    private Integer supplierId;

    @TableField("ROOT_FOOD_CATEGORY_ID")
    private String rootFoodCategoryId;

    @TableField("ROOT_FOOD_CATEGORY_NAME")
    private String rootFoodCategoryName;

    @TableField("FOOD_CATEGORY_ID")
    private String foodCategoryId;

    @TableField("FOOD_CATEGORY_CODE")
    private String foodCategoryCode;

    @TableField("FOOD_CATEGORY_NAME")
    private String foodCategoryName;

    @TableField("NAME")
    private String name;

    @TableField("QUALITY_LEVEL")
    private String qualityLevel;

    @TableField("CAPACITY")
    private BigDecimal capacity;

    @TableField("PRODUCE_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date produceDate;

    @TableField("REMARKS")
    private String remarks;

    //合同签订时间
    @TableField("CONTRACT_SIGN_TIME")
    private Date contractSignTime;
    //是否共享信息
    @TableField("IS_SHARE")
    private String isShare;
    //规格
    @TableField("SPEC")
    private String spec;
    //供应价格
    @TableField("SUPPLIER_PRICE")
    private String supplierPrice;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;
}
