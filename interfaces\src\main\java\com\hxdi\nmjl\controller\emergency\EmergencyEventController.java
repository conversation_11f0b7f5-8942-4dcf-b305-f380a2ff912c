package com.hxdi.nmjl.controller.emergency;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.domain.emergency.EmergencyEvent;
import com.hxdi.nmjl.framework.anno.SysLog;
import com.hxdi.nmjl.condition.emergency.EmergencyEventCondition;
import com.hxdi.nmjl.service.emergency.EmergencyEventService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <应急事件管理接口>
 * <AUTHOR>
 * @version 1.0
 * @since 2025/6/25
 */
@Api(tags = "应急事件管理")
@RestController
@RequestMapping("/emergencyEvent")
public class EmergencyEventController extends BaseController<EmergencyEventService, EmergencyEvent> {

    @ApiOperation("分页查询")
    @GetMapping("/page")
    public ResultBody<Page<EmergencyEvent>> getPages(EmergencyEventCondition condition) {
        return ResultBody.ok().data(bizService.getPages(condition));
    }

    @ApiOperation("列表查询")
    @GetMapping("/list")
    public ResultBody<List<EmergencyEvent>> getList(EmergencyEventCondition condition) {
        return ResultBody.ok().data(bizService.getList(condition));
    }

    @ApiOperation("查看详情")
    @GetMapping("/getDetail")
    public ResultBody<EmergencyEvent> getDetail(@RequestParam String id) {
        return ResultBody.ok().data(bizService.getDetail(id));
    }

    @ApiOperation("删除应急事件信息")
    @PostMapping("/delete")
    public ResultBody<Boolean> delete(@RequestParam String id) {
        return ResultBody.ok().data(bizService.delete(id));
    }

    @ApiOperation(value = "添加/修改")
    @PostMapping("/saveOrUpdate")
    @SysLog
    public ResultBody saveOrUpdate(@RequestBody EmergencyEvent emergencyEvent) {
        if (CommonUtils.isEmpty(emergencyEvent.getId())) {
            bizService.add(emergencyEvent);
        } else {
            bizService.updateById(emergencyEvent);
        }
        return ResultBody.ok();
    }
}
