package com.hxdi.nmjl.controller.emergency;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.domain.emergency.EmergencyPerson;
import com.hxdi.nmjl.framework.anno.SysLog;
import com.hxdi.nmjl.condition.emergency.EmergencyPersonCondition;
import com.hxdi.nmjl.service.emergency.EmergencyPersonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <应急人员管理接口>
 * <AUTHOR>
 * @version 1.0
 * @since 2025/6/25
 */
@Api(tags = "应急人员管理")
@RestController
@RequestMapping("/emergencyPerson")
public class EmergencyPersonController extends BaseController<EmergencyPersonService, EmergencyPerson> {

    @ApiOperation("分页查询")
    @GetMapping("/page")
    public ResultBody<Page<EmergencyPerson>> getPages(EmergencyPersonCondition condition) {
        return ResultBody.ok().data(bizService.getPages(condition));
    }

    @ApiOperation("列表查询")
    @GetMapping("/list")
    public ResultBody<List<EmergencyPerson>> getList(EmergencyPersonCondition condition) {
        return ResultBody.ok().data(bizService.getList(condition));
    }

    @ApiOperation("查看详情")
    @GetMapping("/getDetail")
    public ResultBody<EmergencyPerson> getDetail(@RequestParam String id) {
        return ResultBody.ok().data(bizService.getDetail(id));
    }

    @ApiOperation("删除应急人员信息")
    @PostMapping("/delete")
    public ResultBody<Boolean> delete(@RequestParam String id) {
        return ResultBody.ok().data(bizService.delete(id));
    }

    @ApiOperation(value = "添加/修改")
    @PostMapping("/saveOrUpdate")
    @SysLog
    public ResultBody saveOrUpdate(@RequestBody EmergencyPerson emergencyPerson) {
        if (CommonUtils.isEmpty(emergencyPerson.getId())) {
            bizService.add(emergencyPerson);
        } else {
            bizService.updateById(emergencyPerson);
        }
        return ResultBody.ok();
    }
}
