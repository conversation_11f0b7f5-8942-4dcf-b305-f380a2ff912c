package com.hxdi.nmjl.trash.domain.mobilize;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 动员预案
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("BUSI_MOBILIZE_PLAN")
public class BusiMobilizePlan extends DataEntity<BusiMobilizePlan> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("CODE")
    private String code;

    @TableField("NAME")
    private String name;

    @TableField("DOCUMENT_NUMBER")
    private String documentNumber;

    @TableField("MANAGEMENT_ID")
    private Integer managementId;

    @TableField("MANAGEMENT_NAME")
    private String managementName;

    @TableField("MANAGEMENT_CODE")
    private String managementCode;

    @TableField("PLAN_LEVEL")
    private String planLevel;

    @TableField("EMERGENCY_FUND")
    private String emergencyFund;

    @TableField("SET_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date setTime;

    @TableField("ISSUE_UNIT_ID")
    private Integer issueUnitId;

    @TableField("ISSUE_UNIT")
    private String issueUnit;

    @TableField("CURRENT_VERSION")
    private String currentVersion;

    @TableField("PLAN_TEXT_CONTENT")
    private String planTextContent;

    @TableField("PLAN_TYPE")
    private String planType;

    @TableField("GROUP_CODE")
    private String groupCode;

    @TableField("GROUP_NAME")
    private String groupName;

    @TableField("DUTY_PHONE")
    private String dutyPhone;

    @TableField("GROUP_LEADER")
    private String groupLeader;

    @TableField("GROUP_LEADER_PHONE")
    private String groupLeaderPhone;

    @TableField("ENTRY_PERSON")
    private String entryPerson;

    @TableField("GROUP_MEMBERS")
    private String groupMembers;

    @TableField("WORK_CONTENT")
    private String workContent;

    @TableField("PLAN_STATUS")
    private String planStatus;

    @TableField("CHANGE_PLAN_STATUS_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date changePlanStatusTime;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;
}
