package com.hxdi.nmjl.trash.domain.mobilize;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("BUSI_MACHINE_FACILITIES")
public class BusiMachineFacilities extends DataEntity<BusiMachineFacilities> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("STORE_ID")
    private Integer storeId;

    @TableField("HOUSE_ID")
    private Integer houseId;

    @TableField("CODER")
    private String coder;

    @TableField("NAME")
    private String name;

    @TableField("MACHINE_STATUS")
    private String machineStatus;

    @TableField("MODEL_SPECS")
    private String modelSpecs;

    @TableField("MODE_UNIT")
    private String modeUnit;

    @TableField("BUYER_DATE")
    private Date buyerDate;

    @TableField("MACHINE_POWER")
    private String machinePower;

    @TableField("CAPACITY")
    private BigDecimal capacity;

    @TableField("DIMENSION")
    private String dimension;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;
}
