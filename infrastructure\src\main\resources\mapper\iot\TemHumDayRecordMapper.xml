<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.nmjl.mapper.iot.TemHumDayRecordMapper">
  <resultMap id="BaseResultMap" type="com.hxdi.nmjl.domain.iot.TemHumDayRecord">
    <!--@mbg.generated-->
    <!--@Table B_TEM_HUM_DAY_RECORD-->
    <id column="ID" property="id" />
    <result column="STORE_ID" property="storeId" />
    <result column="STORE_NAME" property="storeName" />
    <result column="ST_ID" property="stId" />
    <result column="ST_NAME" property="stName" />
    <result column="WD" property="wd" />
    <result column="SD" property="sd" />
    <result column="STATISTICAL_DATE" property="statisticalDate" />
    <result column="DATA_HIERARCHY_ID" property="dataHierarchyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, STORE_ID, STORE_NAME, ST_ID, ST_NAME, WD, SD, STATISTICAL_DATE, DATA_HIERARCHY_ID
  </sql>

  <select id="selectListV1" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/>
    FROM B_TEM_HUM_DAY_RECORD
    <where>
      <if test="@plugins.OGNL@isNotEmpty(condition.storeId)">
        AND STORE_ID IN
        <foreach collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.storeId)" item="state" open="(" separator="," close=")">
          #{state}
        </foreach>
      </if>
      <if test="@plugins.OGNL@isNotEmpty(condition.stId)">
        AND ST_ID IN
        <foreach collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.stId)" item="state" open="(" separator="," close=")">
          #{state}
        </foreach>
      </if>
      <if test="@plugins.OGNL@isNotEmpty(condition.startTime)">
        AND STATISTICAL_DATE &gt;= #{condition.startTime}
      </if>
      <if test="@plugins.OGNL@isNotEmpty(condition.endTime)">
        AND STATISTICAL_DATE &lt;= #{condition.endTime}
      </if>
    </where>
  </select>

  <select id="selectPageV1" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/>
    FROM B_TEM_HUM_DAY_RECORD
    <where>
      <if test="@plugins.OGNL@isNotEmpty(condition.storeId)">
        AND STORE_ID IN
        <foreach collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.storeId)" item="state" open="(" separator="," close=")">
          #{state}
        </foreach>
      </if>
      <if test="@plugins.OGNL@isNotEmpty(condition.stId)">
        AND ST_ID IN
        <foreach collection="@com.hxdi.nmjl.utils.DataConvertUtil@ObjToList(condition.stId)" item="state" open="(" separator="," close=")">
          #{state}
        </foreach>
      </if>
      <if test="@plugins.OGNL@isNotEmpty(condition.startTime)">
        AND STATISTICAL_DATE &gt;= #{condition.startTime}
      </if>
      <if test="@plugins.OGNL@isNotEmpty(condition.endTime)">
        AND STATISTICAL_DATE &lt;= #{condition.endTime}
      </if>
    </where>
    ORDER BY STORE_ID,ST_ID,STATISTICAL_DATE desc
  </select>
</mapper>