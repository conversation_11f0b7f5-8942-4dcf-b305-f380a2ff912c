package com.hxdi.nmjl.service.iot.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Maps;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.iot.TemHumWarningConfigCondition;
import com.hxdi.nmjl.domain.common.WarningInfo;
import com.hxdi.nmjl.domain.iot.TemHumRecord;
import com.hxdi.nmjl.dto.iot.WarningResult;
import com.hxdi.nmjl.domain.plan.TemHumWarningConfig;
import com.hxdi.nmjl.enums.MsgTemplateCode;
import com.hxdi.nmjl.enums.TemHumSymbol;
import com.hxdi.nmjl.event.MsgPayload;
import com.hxdi.nmjl.event.MsgSendEvent;
import com.hxdi.nmjl.mapper.iot.TemHumWarningConfigMapper;
import com.hxdi.nmjl.service.common.WarningInfoService;
import com.hxdi.nmjl.service.iot.TemHumWarningConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TemHumWarningConfigServiceImpl extends BaseServiceImpl<TemHumWarningConfigMapper, TemHumWarningConfig> implements TemHumWarningConfigService {

    @Resource
    private WarningInfoService warningInfoService;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    @Override
    public void saveOrUpdateV1(TemHumWarningConfig config) {
        if (CommonUtils.isEmpty(config.getId())) {
            baseMapper.insert(config);
        } else {
            baseMapper.updateById(config);
        }
    }

    @Override
    public List<TemHumWarningConfig> listV1(TemHumWarningConfigCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public IPage<TemHumWarningConfig> pageV1(TemHumWarningConfigCondition condition) {
        return baseMapper.selectPageV1(condition.newPage(), condition);
    }

    @Override
    public void generateWarningMsg(String stId, List<TemHumRecord> recordList) {
        if (recordList.isEmpty() || CommonUtils.isEmpty(stId)) {
            return;
        }

        TemHumWarningConfigCondition condition = new TemHumWarningConfigCondition();
        condition.setStId(stId);
        List<TemHumWarningConfig> list = this.listV1(condition);
        if (list.isEmpty()) {
            return;
        }

        // 获取每个指标及对应的配置
        Map<String, TemHumWarningConfig> indexMap = list.stream()
                .collect(Collectors.toMap(TemHumWarningConfig::getIndexName, item -> item,
                        (existing, replacement) -> replacement));

        //处理数据
        processRecord(recordList, indexMap);
    }

    /**
     * 处理单个仓房的检测记录
     *
     * @param recordList 检测记录列表
     * @param indexMap   指标配置映射
     */
    private void processRecord(List<TemHumRecord> recordList, Map<String, TemHumWarningConfig> indexMap) {

        //将检测记录按指标分为四组：仓温、仓湿、气温、气湿
        Map<String, List<TemHumRecord>> recordMap = recordList.stream()
                .collect(Collectors.groupingBy(this::getIndexType));

        //处理每组数据，获取指定指标项的最大值和最小值，并生成预警信息
        recordMap.entrySet().stream()
                .filter(entry -> !"未知".equals(entry.getKey()))
                .forEach(entry -> {
                    String indexName = entry.getKey();
                    List<TemHumRecord> records = entry.getValue();
                    if (!records.isEmpty()) {
                        try {
                            processIndex(records, indexName, indexMap);
                        } catch (Exception e) {
                            log.error("处理指标 {} 时发生异常: {}", indexName, e.getMessage(), e);
                        }
                    }
                });
    }

    /**
     * 获取记录的指标类型
     */
    private String getIndexType(TemHumRecord record) {
        if (!CommonUtils.isEmpty(record.getCw())) {
            return "仓温";
        } else if (!CommonUtils.isEmpty(record.getCs())) {
            return "仓湿";
        } else if (!CommonUtils.isEmpty(record.getQw())) {
            return "气温";
        } else if (!CommonUtils.isEmpty(record.getQs())) {
            return "气湿";
        } else {
            return "未知";
        }
    }

    /**
     * 处理单个指标的数据
     *
     * @param records   检测记录列表
     * @param indexName 指标名称
     * @param indexMap  指标配置映射
     */
    private void processIndex(List<TemHumRecord> records, String indexName, Map<String, TemHumWarningConfig> indexMap) {
        TemHumWarningConfig config = indexMap.get(indexName);
        if (config == null) {
            return;
        }

        // 获取有效的数值记录，支持多设备数据聚合
        List<Double> values = records.stream()
                .map(record -> getRecordValue(record, indexName))
                .filter(value -> value != null && !Double.isNaN(value))
                .collect(Collectors.toList());

        if (values.isEmpty()) {
            log.warn("指标 {} 没有有效的数值记录", indexName);
            return;
        }

        //获取最大值和最小值 - 支持多设备聚合
        double maxValue = values.stream().mapToDouble(Double::doubleValue).max().orElse(0);
        double minValue = values.stream().mapToDouble(Double::doubleValue).min().orElse(0);
        double avgValue = values.stream().mapToDouble(Double::doubleValue).average().orElse(0);

        log.debug("仓房 {} 指标 {} 统计: 最大值={}, 最小值={}, 平均值={}",
                config.getStId(), indexName, maxValue, minValue, avgValue);

        List<WarningInfo> warningInfoList = new ArrayList<>();
        List<MsgPayload> payloadList = new ArrayList<>();

        //生成预警信息
        generateWarningMsg(warningInfoList, payloadList, records, indexName, config, maxValue, minValue);

        // 批量保存预警信息
        if (!warningInfoList.isEmpty()) {
            try {
                warningInfoService.saveBatch(warningInfoList);
                log.info("仓房 {} 指标 {} 生成 {} 条预警信息", config.getStId(), indexName, warningInfoList.size());
            } catch (Exception e) {
                log.error("保存预警信息失败: {}", e.getMessage(), e);
            }
        }

        // 发布消息事件
        if (!payloadList.isEmpty()) {
            try {
                eventPublisher.publishEvent(new MsgSendEvent(this, payloadList));
                log.info("仓房 {} 指标 {} 发布 {} 条预警消息", config.getStId(), indexName, payloadList.size());
            } catch (Exception e) {
                log.error("发布预警消息失败: {}", e.getMessage(), e);
            }
        }
    }

    /**
     * 生成预警信息
     *
     * @param records   检测记录列表
     * @param indexName 指标名称
     * @param config    指标配置
     * @param maxValue  最大值
     * @param minValue  最小值
     */
    private void generateWarningMsg(List<WarningInfo> warningInfoList, List<MsgPayload> payloadList, List<TemHumRecord> records, String indexName, TemHumWarningConfig config, double maxValue, double minValue) {
        // 创建预警信息
        WarningInfo warningInfo = new WarningInfo();
        warningInfo.setWarningType("2");
        warningInfo.setTriMode(1);
        warningInfo.setStoreId(config.getStoreId());
        warningInfo.setStoreName(config.getStoreName());

        TemHumSymbol symbol = TemHumSymbol.getByLabel(getIndexLabel(indexName));
        if (symbol == null) {
            log.warn("未找到指标 {} 对应的符号", indexName);
            return;
        }

        // 检查预警阈值
        WarningResult warningResult = checkWarningLimits(config, maxValue, minValue);
        if (!warningResult.isWarning()) {
            // 没有超过上下限
            return;
        }

        warningInfo.setMsg(String.format("库点%s下属仓房%s%s已%s：%s%s，当前 %s 为：%s%s",
                config.getStoreName(), config.getStName(), symbol.getName(),
                warningResult.getLimitType(), warningResult.getLimitValue(), symbol.getSymbol(),
                symbol.getName(), warningResult.getCurrentValue(), symbol.getSymbol()));

        //关联记录ID设置为检测记录的messageId
        warningInfo.setRefId(records.get(0).getMessageId());
        warningInfo.setDisposeState(0);
        //保持预警信息与库存数据权限一致，便于后面进行查询
        warningInfo.setCreateId(config.getCreateId());
        warningInfo.setTenantId(config.getTenantId());
        warningInfo.setDataHierarchyId(config.getDataHierarchyId());
        warningInfoList.add(warningInfo);

        // 创建消息
        MsgPayload payload = new MsgPayload();
        payload.setMsgCode(MsgTemplateCode.TEM_HUM_ALARM.getCode());
        payload.setReceiver(config.getUsers());

        //预警模版： 库点${storeName}下属仓房${stName}${indexName}已${limitType}：${limitNum}${indexLabol}，当前${indexName}为: ${currentValue}${indexLabol}
        Map<String, String> jsonParams = Maps.newHashMap();
        jsonParams.put("storeName", config.getStoreName());
        jsonParams.put("stName", config.getStName());
        jsonParams.put("indexName", symbol.getName());
        jsonParams.put("limitType", warningResult.getLimitType());
        jsonParams.put("limitNum", warningResult.getLimitValue());
        jsonParams.put("indexLabol", symbol.getSymbol());
        jsonParams.put("currentValue", warningResult.getCurrentValue());
        payload.setJsonParams(jsonParams);

        payloadList.add(payload);
    }

    /**
     * 获取指标对应的标签
     */
    private String getIndexLabel(String indexName) {
        switch (indexName) {
            case "仓温":
                return "cw";
            case "仓湿":
                return "cs";
            case "气温":
                return "qw";
            case "气湿":
                return "qs";
            default:
                return null;
        }
    }

    /**
     * 检查预警阈值
     */
    private WarningResult checkWarningLimits(TemHumWarningConfig config, double maxValue, double minValue) {
        try {
            // 检查下限
            if (!CommonUtils.isEmpty(config.getMinValue())) {
                double minLimit = Double.parseDouble(config.getMinValue());
                if (minValue < minLimit) {
                    return new WarningResult(true, true, config.getMinValue(), String.valueOf(minValue));
                }
            }

            // 检查上限
            if (!CommonUtils.isEmpty(config.getMaxValue())) {
                double maxLimit = Double.parseDouble(config.getMaxValue());
                if (maxValue > maxLimit) {
                    return new WarningResult(true, false, config.getMaxValue(), String.valueOf(maxValue));
                }
            }

            return new WarningResult(false, false, null, null);
        } catch (NumberFormatException e) {
            log.error("预警配置中的阈值格式错误: {}", e.getMessage());
            return new WarningResult(false, false, null, null);
        }
    }

    /**
     * 从记录中提取指定指标的数值
     */
    private Double getRecordValue(TemHumRecord record, String indexName) {
        try {
            switch (indexName) {
                case "仓温":
                    return CommonUtils.isEmpty(record.getCw()) ? null : Double.parseDouble(record.getCw());
                case "仓湿":
                    return CommonUtils.isEmpty(record.getCs()) ? null : Double.parseDouble(record.getCs());
                case "气温":
                    return CommonUtils.isEmpty(record.getQw()) ? null : Double.parseDouble(record.getQw());
                case "气湿":
                    return CommonUtils.isEmpty(record.getQs()) ? null : Double.parseDouble(record.getQs());
                default:
                    return null;
            }
        } catch (NumberFormatException e) {
            log.warn("指标 {} 的数值格式错误: {}", indexName, e.getMessage());
            return null;
        }
    }

}
