package com.hxdi.nmjl.trash.domain.quality;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 质检报告值
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("BUSI_QUALITY_REPORT_RESULT")
public class BusiQualityReportResult extends DataEntity<BusiQualityReportResult> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("QUALITY_REPORT_ID")
    private Integer qualityReportId;

    @TableField("STANDARD_CODE")
    private String standardCode;

    @TableField("TARGET_NAME")
    private String targetName;

    @TableField("COMPARE_SYMBOL")
    private String compareSymbol;

    @TableField("TARGET_VALUE")
    private String targetValue;

    @TableField("ACTUAL_VALUE")
    private String actualValue;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;
}
