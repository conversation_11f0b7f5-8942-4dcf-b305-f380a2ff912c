package com.hxdi.nmjl.mapper.iot;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.condition.iot.TemHumDayRecordCondition;
import com.hxdi.nmjl.domain.iot.TemHumDayRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TemHumDayRecordMapper extends SuperMapper<TemHumDayRecord> {
    @DataPermission
    List<TemHumDayRecord> selectListV1(@Param("condition") TemHumDayRecordCondition condition);

    @DataPermission
    Page<TemHumDayRecord> selectPageV1(@Param("condition") TemHumDayRecordCondition condition, @Param("page") Page<TemHumDayRecord> page);
}