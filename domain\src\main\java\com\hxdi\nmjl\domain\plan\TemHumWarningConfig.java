package com.hxdi.nmjl.domain.plan;

import com.baomidou.mybatisplus.annotation.*;
import com.hxdi.nmjl.base.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;


@Getter
@Setter
@ApiModel(description = "温湿度预警参数配置")
@TableName(value = "B_TEM_HUM_WARNING_CONFIG")
public class TemHumWarningConfig extends Entity<TemHumWarningConfig> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 库点ID
     */
    @TableField(value = "STORE_ID")
    @ApiModelProperty(value = "库点ID")
    private String storeId;

    /**
     * 库点名称
     */
    @TableField(value = "STORE_NAME")
    @ApiModelProperty(value = "库点名称")
    private String storeName;

    /**
     * 仓房ID
     */
    @TableField(value = "ST_ID")
    @ApiModelProperty(value = "仓房ID")
    private String stId;

    /**
     * 仓房
     */
    @TableField(value = "ST_NAME")
    @ApiModelProperty(value = "仓房")
    private String stName;

    /**
     * 任务类型：字典SBGJLX
     */
    @TableField(value = "TASK_TYPE")
    @ApiModelProperty(value = "任务类型：字典SBGJLX")
    private String taskType;

    /**
     * 指标:字典SBZB 仓温CW 仓湿CS
     */
    @TableField(value = "INDEX_NAME")
    @ApiModelProperty(value = "指标:字典SBZB")
    private String indexName;

    /**
     * 上限
     */
    @TableField(value = "MAX_VALUE")
    @ApiModelProperty(value = "上限")
    private String maxValue;

    /**
     * 下限
     */
    @TableField(value = "MIN_VALUE")
    @ApiModelProperty(value = "下限")
    private String minValue;

    /**
     * 设备类型:字典SBLX
     */
    @Deprecated
    @TableField(value = "DEVICE_TYPE")
    @ApiModelProperty(value = "设备类型:字典SBLX")
    private String deviceType;

    /**
     * 自定义消息
     */
    @Deprecated
    @TableField(value = "MSG")
    @ApiModelProperty(value = "自定义消息")
    private String msg;

    /**
     * 推送方式：字典XXTSFS
     */
    @Deprecated
    @TableField(value = "PUSH_TYPE")
    @ApiModelProperty(value = "推送方式：字典XXTSFS")
    private String pushType;

    /**
     * 接收用户角色,分隔
     */
    @Deprecated
    @TableField(value = "ROLES")
    @ApiModelProperty(value = "接收用户角色,分隔")
    private String roles;

    /**
     * 接收用户,分隔
     */
    @TableField(value = "USERS")
    @ApiModelProperty(value = "接收用户,分隔")
    private String users;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建id")
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新id")
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID")
    @ApiModelProperty(value = "租户id")
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "组织")
    private String dataHierarchyId;
}