package com.hxdi.nmjl.trash.domain.product;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 应急采购顶大
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("BUSI_TMP_PURCHASE_ORDER")
public class BusiTmpPurchaseOrder extends DataEntity<BusiTmpPurchaseOrder> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("EVENT_ID")
    private Integer eventId;

    @TableField("EVENT_CODE")
    private String eventCode;

    @TableField("CODE")
    private String code;

    @TableField("ROOT_FOOD_CATEGORY_ID")
    private String rootFoodCategoryId;

    @TableField("ROOT_FOOD_CATEGORY_NAME")
    private String rootFoodCategoryName;

    @TableField("FOOD_CATEGORY_ID")
    private String foodCategoryId;

    @TableField("FOOD_CATEGORY_CODE")
    private String foodCategoryCode;

    @TableField("FOOD_CATEGORY_NAME")
    private String foodCategoryName;

    @TableField("BRAND_ID")
    private String brandId;

    @TableField("BRAND_NAME")
    private String brandName;

    @TableField("SPEC")
    private String spec;

    @TableField("AMOUNT")
    private BigDecimal amount;

    @TableField("QUANTITY")
    private Integer quantity;

    @TableField("TOTAL_AMOUNT")
    private BigDecimal totalAmount;

    @TableField("SUPPLIER_ID")
    private Integer supplierId;

    @TableField("SUPPLIER_NAME")
    private String supplierName;

    @TableField("RECEIVE_UNIT")
    private String receiveUnit;

    @TableField("ORDER_CREATE_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date orderCreateTime;

    @TableField("ORDER_STATUS")
    private String orderStatus;

    @TableField("SETTLE_STATUS")
    private String settleStatus;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;
}
