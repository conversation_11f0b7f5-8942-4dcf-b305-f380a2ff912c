package com.hxdi.nmjl.trash.domain.station;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 配送
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("BUSI_STATION_DELIVERY")
public class BusiStationDelivery extends DataEntity<BusiStationDelivery> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("STATION_ID")
    private Integer stationId;

    @TableField("STATION_NAME")
    private String stationName;

    @TableField("CAR_ID")
    private Integer carId;

    @TableField("CAR_CODER")
    private String carCoder;

    @TableField("CAR_MODEL")
    private String carModel;

    @TableField("CAR_LOADER")
    private String carLoader;

    @TableField("FOLLOW_BY")
    private Integer followBy;

    @TableField("FOLLOW_NAME")
    private String followName;

    @TableField("HOME_WAREHOUSE")
    private String homeWarehouse;

    @TableField("CAR_NUM")
    private String carNum;

    @TableField("CAR_TRANSPORT")
    private String carTransport;

    @TableField("CONTACT")
    private String contact;

    @TableField("DELIVERY_GOODS")
    private String deliveryGoods;

    @TableField("GOODS_SPEC")
    private String goodsSpec;

    @TableField("DELIVERY_QUANTITY")
    private BigDecimal deliveryQuantity;

    @TableField("START_DELIVERY_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDeliveryDate;

    @TableField("END_DELIVERY_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDeliveryDate;

    @TableField("CUSTOMER_TYPE")
    private String customerType;

    @TableField("ARMY_NAME")
    private String armyName;

    @TableField("PROVINCE_ID")
    private Integer provinceId;

    @TableField("PROVINCE")
    private String province;

    @TableField("CITY_ID")
    private Integer cityId;

    @TableField("CITY")
    private String city;

    @TableField("COUNTY_ID")
    private Integer countyId;

    @TableField("COUNTY")
    private String county;

    @TableField("ADDRESS")
    private String address;

    @TableField("DELIVERY_STATUS")
    private String deliveryStatus;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;

    @TableField("CAR_STATUS")
    private String carStatus;

    @TableField("CODE")
    private String code;
}
