package com.hxdi.nmjl.trash.domain.raise;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 确认单
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("BUSI_RAISE_BID")
public class BusiRaiseBid extends DataEntity<BusiRaiseBid> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("CODER")
    private String coder;

    @TableField("NAME")
    private String name;

    @TableField("YEAR")
    private String year;

    @TableField("ROOT_FOOD_CATEGORY_ID")
    private String rootFoodCategoryId;

    @TableField("ROOT_FOOD_CATEGORY_NAME")
    private String rootFoodCategoryName;

    @TableField("FOOD_CATEGORY_ID")
    private String foodCategoryId;

    @TableField("FOOD_CATEGORY_CODE")
    private String foodCategoryCode;

    @TableField("FOOD_CATEGORY_NAME")
    private String foodCategoryName;

    @TableField("BRAND_ID")
    private Integer brandId;

    @TableField("BRAND_NAME")
    private String brandName;

    @TableField("MIN_QUANTITY")
    private BigDecimal minQuantity;

    @TableField("MAX_QUANTITY")
    private BigDecimal maxQuantity;

    @TableField("OPEN_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date openDate;

    @TableField("DONE_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date doneDate;

    @TableField("OFFICE_ID")
    private Integer officeId;

    @TableField("OFFICE_NAME")
    private String officeName;

    @TableField("PURCHASE_ID")
    private Integer purchaseId;

    @TableField("PURCHASE_NAME")
    private String purchaseName;

    @TableField("BAK_NAME")
    private String bakName;

    @TableField("SUPPLIER_ID")
    private String supplierId;

    @TableField("SUPPLIER_NAME")
    private String supplierName;

    @TableField("SUPPLIER_CODE")
    private String supplierCode;

    @TableField("ENTRUST_UNIT")
    private String entrustUnit;

    @TableField("ENTRUST_TARGET")
    private String entrustTarget;

    @TableField("IMPLEMENT_UNIT")
    private String implementUnit;

    @TableField("DEAL_NUM")
    private BigDecimal dealNum;

    @TableField("DEAL_MIN_NUM")
    private BigDecimal dealMinNum;

    @TableField("DEAL_MAX_NUM")
    private BigDecimal dealMaxNum;

    @TableField("DEAL_PRICE")
    private BigDecimal dealPrice;

    @TableField("DEAL_TOTAL")
    private BigDecimal dealTotal;

    @TableField("DEAL_MIN_TOTAL")
    private BigDecimal dealMinTotal;

    @TableField("DEAL_MAX_TOTAL")
    private BigDecimal dealMaxTotal;

    @TableField("QUALITY_LEVEL")
    private String qualityLevel;

    @TableField("RESULT_ISSUE_STATUS")
    private String resultIssueStatus;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;

    /**
     * 供应商编码
     */
    @TableField("SUPPLIER_NUMBER")
    private String supplierNumber;

    /**
     * 总价
     */
    @TableField("TOTAL")
    private String total;

    /**
     * 中标得分
     */
    @TableField("BIT_SCORE")
    private String bitScore;
}
