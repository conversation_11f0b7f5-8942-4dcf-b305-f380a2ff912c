package com.hxdi.nmjl.trash.domain.raise;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 追溯信息（国产化）
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("BUSI_STORE_STOCK_TRACEBACK")
public class BusiStoreStockTraceback extends DataEntity<BusiStoreStockTraceback> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("ROOT_FOOD_CATEGORY_ID")
    private String rootFoodCategoryId;

    @TableField("ROOT_FOOD_CATEGORY_NAME")
    private String rootFoodCategoryName;

    @TableField("FOOD_CATEGORY_ID")
    private String foodCategoryId;

    @TableField("FOOD_CATEGORY_CODE")
    private String foodCategoryCode;

    @TableField("FOOD_CATEGORY_NAME")
    private String foodCategoryName;

    @TableField("ORIGIN")
    private String origin;

    @TableField("HARVEST_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date harvestTime;

    @TableField("DRUG_USE_RECORD")
    private String drugUseRecord;

    @TableField("DRY_TYPE")
    private String dryType;

    @TableField("STORAGE_TYPE")
    private String storageType;

    @TableField("INSECT_PROTECT_RECORD")
    private String insectProtectRecord;

    @TableField("STORAGE_QUANTITY")
    private BigDecimal storageQuantity;

    @TableField("PROCESS_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date processTime;

    @TableField("BATCH")
    private String batch;

    @TableField("BATCH_QUANTITY")
    private BigDecimal batchQuantity;

    @TableField("PRODUCTION_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date productionDate;

    @TableField("PROCESS_TECHNOLOGY")
    private String processTechnology;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;
}
