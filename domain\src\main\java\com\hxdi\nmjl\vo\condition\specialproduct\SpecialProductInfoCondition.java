package com.hxdi.nmjl.vo.condition.specialproduct;


import com.hxdi.common.core.model.QueryCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;


/**
 * 地方特色产品信息查询条件
 */
@ApiModel(description = "地方特色产品信息查询条件")
@Getter
@Setter
public class SpecialProductInfoCondition extends QueryCondition {


    @ApiModelProperty(value = "产品名称（模糊查询）")
    private String productNameLike;

    @ApiModelProperty(value = "质量等级")
    private String grade;

    @ApiModelProperty(value = "产地（模糊查询）")
    private String originLike;

    @ApiModelProperty(value = "客户名称（模糊查询）")
    private String clientNameLike;

    @ApiModelProperty(value = "客户ID")
    private String clientId;

    @ApiModelProperty(value = "审核状态：0-审核中，1-已审核，2-驳回")
    private Integer approveStatus;

    @ApiModelProperty(value = "上线状态：0-待发布，1-已发布")
    private Integer publishState;

}

