package com.hxdi.nmjl.domain.inventory;


import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 库存调配管理
 * @TableName B_INVENTORY_ALLOCATION
 */
@TableName(value ="B_INVENTORY_ALLOCATION")
@Data
public class InventoryAllocation {
    /**
     * 主键
     */
    @TableId(value = "ID",type = IdType.ASSIGN_ID)
    @ApiModelProperty(value="主键")
    private String id;

    /**
     * 调配单号
     */
    @TableField(value = "ALLOCATE_CODE")
    @ApiModelProperty(value="调配单号")
    private String allocateCode;

    /**
     * 调出军供站ID
     */
    @TableField(value = "OUT_STORE_ID")
    @ApiModelProperty(value="调出军供站ID")
    private String outStoreId;

    /**
     * 调出军供站名称
     */
    @TableField(value = "OUT_STORE_NAME")
    @ApiModelProperty(value="调出军供站名称")
    private String outStoreName;

    /**
     * 调入军供站ID
     */
    @TableField(value = "IN_STORE_ID")
    @ApiModelProperty(value="调配单号")
    private String inStoreId;

    /**
     * 调入军供站名称
     */
    @TableField(value = "IN_STORE_NAME")
    @ApiModelProperty(value="调配单号")
    private String inStoreName;

    /**
     * 调配日期
     */
    @TableField(value = "ALLOCATE_TIME")
    @ApiModelProperty(value="调配单号")
    private Date allocateTime;

    /**
     * 调配状态: 0-未开始，1-调配中，2-已完成
     */
    @TableField(value = "ALLOCATE_STATE")
    @ApiModelProperty(value="调配单号")
    private Integer allocateState;

    /**
     * 审核状态：0-未审核，1-已审核，2-驳回
     */
    @TableField(value = "APPROVE_STATUS")
    @ApiModelProperty(value="调配单号")
    private Integer approveStatus;

    /**
     * 审批人
     */
    @TableField(value = "APPROVER")
    @ApiModelProperty(value="调配单号")
    private String approver;

    /**
     * 审批时间
     */
    @TableField(value = "APPROVE_TIME")
    @ApiModelProperty(value="调配单号")
    private Date approveTime;

    /**
     * 审批意见
     */
    @TableField(value = "APPROVE_OPINION")
    @ApiModelProperty(value="调配单号")
    private String approveOpinion;

    /**
     * 备注
     */
    @TableField(value = "NOTES")
    @ApiModelProperty(value="调配单号")
    private String notes;

    /**
     * 附件
     */
    @TableField(value = "ATTACHMENT")
    @ApiModelProperty(value="调配单号")
    private String attachment;

    /**
     * 状态:1-有效，0-删除
     */
    @TableField(value = "ENABLED")
    @ApiModelProperty(value="调配单号")
    private Integer enabled;

    /**
     * 创建日期
     */
    @TableField(value = "CREATE_TIME")
    @ApiModelProperty(value="调配单号")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME")
    @ApiModelProperty(value="调配单号")
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID")
    @ApiModelProperty(value="调配单号")
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID")
    @ApiModelProperty(value="调配单号")
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID")
    @ApiModelProperty(value="调配单号")
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID")
    @ApiModelProperty(value="调配单号")
    private String dataHierarchyId;

    @ApiModelProperty(value="调配计划详情信息")
    @TableField(exist = false)
    private List<InventoryAllocationDetail> detailList;


}