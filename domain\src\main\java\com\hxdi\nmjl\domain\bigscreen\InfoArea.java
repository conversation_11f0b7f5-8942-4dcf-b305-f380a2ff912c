package com.hxdi.nmjl.domain.bigscreen;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @program: nmjl-service
 * @description: 地区信息表
 * @author: 王贝强
 * @create: 2025-07-28 13:42
 */
@Getter
@Setter
public class InfoArea implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("ID")
    @ApiModelProperty(value = "主键")
    private String id;

    @TableField("AREA_CODE")
    @ApiModelProperty(value = "区域编码")
    private String areaCode;

    @TableField("PARENT_ID")
    @ApiModelProperty(value = "父级编码")
    private Integer parentId;

    @TableField("AREA_NAME")
    @ApiModelProperty(value = "区域名称")
    private String areaName;

    @TableField("TREE_LEVEL")
    @ApiModelProperty(value = "树层级")
    private Integer treeLevel;

    @TableField("TREE_LEAF")
    @ApiModelProperty(value = "是否叶子节点")
    private String treeLeaf;

    @TableField("TREE_SORT")
    @ApiModelProperty(value = "排序")
    private Integer treeSort;

//-----------------非实体字段-----------------

    @TableField(exist = false)
    @ApiModelProperty(value = "子节点")
    private List<InfoArea> children;
}
