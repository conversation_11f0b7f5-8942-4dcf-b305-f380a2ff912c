package com.hxdi.nmjl.trash.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 油罐
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("INFO_STORE_OIL_TANK")
public class InfoStoreOilTank extends DataEntity<InfoStoreOilTank> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("STORE_ID")
    private Integer storeId;

    @TableField("NAME")
    private String name;

    @TableField("CODER")
    private String coder;

    @TableField("OIL_LEVEL")
    private String oilLevel;

    @TableField("CAPACITY")
    private BigDecimal capacity;

    @TableField("GROUP_CAPACITY")
    private BigDecimal groupCapacity;

    @TableField("BUILD_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date buildDate;

    @TableField("TANK_HIGH")
    private BigDecimal tankHigh;

    @TableField("TANK_WIDE")
    private BigDecimal tankWide;

    @TableField("TANK_DIAM")
    private BigDecimal tankDiam;

    @TableField("TANK_STATUS")
    private String tankStatus;

    @TableField("TANK_USE_STATUS")
    private String tankUseStatus;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;
}
