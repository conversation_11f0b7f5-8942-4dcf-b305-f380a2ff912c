package com.hxdi.nmjl.mapper.iot;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.condition.iot.TemHumWarningConfigCondition;
import com.hxdi.nmjl.domain.plan.TemHumWarningConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TemHumWarningConfigMapper extends SuperMapper<TemHumWarningConfig> {
    List<TemHumWarningConfig> selectListV1(@Param("condition") TemHumWarningConfigCondition condition);

    IPage<TemHumWarningConfig> selectPageV1(Page<TemHumWarningConfig> page, @Param("condition") TemHumWarningConfigCondition condition);
}