package com.hxdi.nmjl.service.base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.nmjl.domain.base.SupplierEvaluation;
import com.hxdi.nmjl.vo.condition.base.SupplierEvaluationCondition;

import java.util.List;

/**
 * 供应商评价服务接口
 */
public interface SupplierEvaluationService extends IBaseService<SupplierEvaluation> {

    /**
     * 查询供应商评价详情
     *
     * @param id 供应商评价ID
     * @return SupplierEvaluation
     */
    SupplierEvaluation getDetail(String id);

    /**
     * 分页查询供应商评价信息
     *
     * @param condition 查询条件
     * @return Page<SupplierEvaluation>
     */
    Page<SupplierEvaluation> getPages(SupplierEvaluationCondition condition);

    /**
     * 列表查询供应商评价信息
     *
     * @param condition 查询条件
     * @return List<SupplierEvaluation>
     */
    List<SupplierEvaluation> getList(SupplierEvaluationCondition condition);

    /**
     * 新增供应商评价信息
     *
     * @param supplierEvaluation 供应商评价信息
     */
    void add(SupplierEvaluation supplierEvaluation);

    /**
     * 更新供应商评价信息
     *
     * @param supplierEvaluation 供应商评价信息
     */
    void update(SupplierEvaluation supplierEvaluation);

    /**
     * 删除供应商评价信息
     *
     * @param id 供应商评价ID
     * @return boolean
     */
    boolean delete(String id);
}
