package com.hxdi.nmjl.controller.specialproduct;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.domain.specialproduct.SpecialProductInfo;
import com.hxdi.nmjl.service.specialproduct.SpecialProductInfoService;
import com.hxdi.nmjl.vo.condition.specialproduct.SpecialProductInfoCondition;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 地方特色产品信息管理
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/16
 */
@Api(tags = "地方特色产品信息管理")
@RestController
@RequestMapping("/specialProductInfo")
public class SpecialProductInfoController extends BaseController<SpecialProductInfoService, SpecialProductInfo> {

    @ApiOperation("分页查询产品")
    @GetMapping("/page")
    public ResultBody<Page<SpecialProductInfo>> page(SpecialProductInfoCondition condition) {
        return ResultBody.ok().data(bizService.pages(condition));
    }

    @ApiOperation("列表查询产品")
    @GetMapping("/list")
    public ResultBody<List<SpecialProductInfo>> list(SpecialProductInfoCondition condition) {
        return ResultBody.ok().data(bizService.lists(condition));
    }

    @ApiOperation("保存/修改产品")
    @PostMapping("/saveOrUpdate")
    public ResultBody saveOrUpdate(@RequestBody SpecialProductInfo productInfo) {
        if (CommonUtils.isEmpty(productInfo.getId())) {
            bizService.create(productInfo);
        } else {
            bizService.update(productInfo);
        }
        return ResultBody.ok();
    }

    @ApiOperation("查看产品详情")
    @GetMapping("/getDetail")
    public ResultBody<SpecialProductInfo> getDetail(@RequestParam String productId) {
        return ResultBody.ok().data(bizService.getDetail(productId));
    }

    @ApiOperation("提交产品")
    @PostMapping("/submit")
    public ResultBody submit(@RequestParam String productId) {
        bizService.submit(productId);
        return ResultBody.ok();
    }

    @ApiOperation("审核产品")
    @PostMapping("/approve")
    public ResultBody approve(@RequestParam String productId,
                              @RequestParam Integer approveStatus,
                              @RequestParam String opinion) {
        bizService.approve(productId, approveStatus, opinion);
        return ResultBody.ok();
    }

    @ApiOperation("删除产品")
    @PostMapping("/remove")
    public ResultBody remove(@RequestParam String id) {
        bizService.remove(id);
        return ResultBody.ok();
    }

    @ApiOperation("发布产品")
    @PostMapping("/publish")
    public ResultBody publish(@RequestParam String productId) {
        bizService.publish(productId);
        return ResultBody.ok();
    }

    @ApiOperation("取消发布产品")
    @PostMapping("/unpublish")
    public ResultBody unpublish(@RequestParam String productId) {
        bizService.unpublish(productId);
        return ResultBody.ok();
    }
}

