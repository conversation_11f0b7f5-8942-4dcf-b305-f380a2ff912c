package com.hxdi.nmjl.service.specialproduct.Impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.nmjl.domain.specialproduct.SpecialProductContent;
import com.hxdi.nmjl.domain.specialproduct.SpecialProductInfo;
import com.hxdi.nmjl.mapper.specialproduct.SpecialProductContentMapper;
import com.hxdi.nmjl.service.specialproduct.SpecialProductContentService;
import com.hxdi.nmjl.service.specialproduct.SpecialProductInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class SpecialProductContentServiceImpl extends BaseServiceImpl<SpecialProductContentMapper, SpecialProductContent> implements SpecialProductContentService {

    @Autowired
    private SpecialProductInfoService specialProductInfoService;

    @Override
    public void create(String productId, String content) {
        SpecialProductContent specialProductContent = new SpecialProductContent();
        // 创建描述信息
        specialProductContent.setProductId(productId);
        specialProductContent.setContent(content);
        // 保存描述
        this.save(specialProductContent);
    }


    @Override
    public void update(String productId, String content) {
        // 查询现有描述
        SpecialProductContent specialProductContent = this.getById(productId);
        if (specialProductContent == null) {
            create(productId, content);
        } else {
            // 更新描述内容
            specialProductContent.setContent(content);
            this.updateById(specialProductContent);
        }
    }


    @Override
    public String getDetail(String productId) {
        LambdaQueryWrapper<SpecialProductContent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpecialProductContent::getProductId, productId);
        SpecialProductContent specialProductContent = baseMapper.selectOne(queryWrapper);
        return specialProductContent != null ? specialProductContent.getContent() : "";
    }

    @Override
    public void remove(String productId) {
        // 校验产品状态
        SpecialProductInfo product = specialProductInfoService.getById(productId);
        if (product.getApproveStatus() == 1) {
            throw new BaseException("产品已审核通过，不允许删除描述");
        }

        // 删除描述
        LambdaQueryWrapper<SpecialProductContent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpecialProductContent::getProductId, productId);
        int rows = baseMapper.delete(queryWrapper);
        if (rows == 0) {
            throw new BaseException("产品描述不存在，删除失败");
        }
        log.info("产品描述删除成功，产品ID：{}", productId);
    }

}
