package com.hxdi.nmjl.trash.domain.supply;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.entity.DataEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 销售合同
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("BUSI_SUPPLY_SALES_CONTRACT")
public class BusiSupplySalesContract extends DataEntity<BusiSupplySalesContract> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("ORDER_ID")
    private Integer orderId;

    @TableField("CATEGORY")
    private String category;

    @TableField("VARIETY")
    private String variety;

    @TableField("CUSTOMER_NAME")
    private String customerName;

    @TableField("BRAND")
    private String brand;

    @TableField("NAME")
    private String name;

    @TableField("SPEC")
    private String spec;

    @TableField("QUANTITY")
    private Integer quantity;

    @TableField("AMOUNT")
    private BigDecimal amount;

    @TableField("SIGN_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date signDate;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;

    @ApiModelProperty(value = "合同编号")
    @TableField("CONTRACT_CODE")
    private String contractCode;

    @ApiModelProperty(value = "供应方")
    @TableField("SUPPLY")
    private String supply;

    @ApiModelProperty(value = "需求方")
    @TableField("DEMANDER")
    private String demander;

    @ApiModelProperty(value = "粮食性质")
    @TableField("FOODSTUFF_TYPE")
    private String foodstuffType;

    @ApiModelProperty(value = "粮食等级")
    @TableField("FOODSTUFF_LEVEL")
    private String foodstuffLevel;

    @ApiModelProperty(value = "粮食单价")
    @TableField("PRICE")
    private BigDecimal price;
}
