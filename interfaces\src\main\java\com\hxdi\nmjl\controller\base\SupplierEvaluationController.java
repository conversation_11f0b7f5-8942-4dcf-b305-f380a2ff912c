package com.hxdi.nmjl.controller.base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.domain.base.SupplierEvaluation;
import com.hxdi.nmjl.framework.anno.SysLog;
import com.hxdi.nmjl.service.base.SupplierEvaluationService;
import com.hxdi.nmjl.vo.condition.base.SupplierEvaluationCondition;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <供应商评价接口>
 * <AUTHOR>
 * @version 1.0
 * @since 2025/6/25
 */
@Api(tags = "供应商评价")
@RestController
@RequestMapping("/supplierEvaluation")
public class SupplierEvaluationController extends BaseController<SupplierEvaluationService, SupplierEvaluation> {

    @ApiOperation("分页查询")
    @GetMapping("/page")
    public ResultBody<Page<SupplierEvaluation>> getPages(SupplierEvaluationCondition condition) {
        return ResultBody.ok().data(bizService.getPages(condition));
    }

    @ApiOperation("列表查询")
    @GetMapping("/list")
    public ResultBody<List<SupplierEvaluation>> getList(SupplierEvaluationCondition condition) {
        return ResultBody.ok().data(bizService.getList(condition));
    }

    @ApiOperation("查看详情")
    @GetMapping("/getDetail")
    public ResultBody<SupplierEvaluation> getDetail(@RequestParam String id) {
        return ResultBody.ok().data(bizService.getDetail(id));
    }

    @ApiOperation("删除供应商评价")
    @PostMapping("/delete")
    public ResultBody<Boolean> delete(@RequestParam String id) {
        return ResultBody.ok().data(bizService.delete(id));
    }

    @ApiOperation(value = "添加/修改")
    @PostMapping("/saveOrUpdate")
    @SysLog
    public ResultBody saveOrUpdate(@RequestBody SupplierEvaluation supplierEvaluation){
        if(CommonUtils.isEmpty(supplierEvaluation.getId())){
            bizService.add(supplierEvaluation);
        }else {
            bizService.update(supplierEvaluation);
        }
        return ResultBody.ok();
    }
}
