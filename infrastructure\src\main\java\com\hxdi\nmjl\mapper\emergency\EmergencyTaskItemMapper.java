package com.hxdi.nmjl.mapper.emergency;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.condition.emergency.EmergencyTaskItemCondition;
import com.hxdi.nmjl.domain.emergency.EmergencyTaskItem;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 紧急任务项数据访问接口
 */
public interface EmergencyTaskItemMapper extends SuperMapper<EmergencyTaskItem> {

    Page<EmergencyTaskItem> selectPageV1(Page<EmergencyTaskItem> page, @Param("condition") EmergencyTaskItemCondition condition);

    List<EmergencyTaskItem> selectListV1(@Param("condition") EmergencyTaskItemCondition condition);
}