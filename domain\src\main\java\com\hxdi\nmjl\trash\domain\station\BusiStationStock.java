package com.hxdi.nmjl.trash.domain.station;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 出入库
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("BUSI_STATION_STOCK")
public class BusiStationStock extends DataEntity<BusiStationStock> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("CODE")
    private String code;

    @TableField("TYPE")
    private String type;

    @TableField("PLAN_CODE")
    private String planCode;

    @TableField("DO_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date doDate;

    @TableField("ORDER_CODE")
    private String orderCode;

    @TableField("PURCHASE_NAME")
    private String purchaseName;

    @TableField("CONTRACT_ID")
    private Integer contractId;

    @TableField("CONTRACT_CODE")
    private String contractCode;

    @TableField("BATCH")
    private String batch;

    @TableField("PRODUCTION_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date productionDate;

    @TableField("ORIGIN")
    private String origin;

    @TableField("ROOT_FOOD_CATEGORY_ID")
    private String rootFoodCategoryId;

    @TableField("ROOT_FOOD_CATEGORY_NAME")
    private String rootFoodCategoryName;

    @TableField("FOOD_CATEGORY_ID")
    private String foodCategoryId;

    @TableField("FOOD_CATEGORY_CODE")
    private String foodCategoryCode;

    @TableField("FOOD_CATEGORY_NAME")
    private String foodCategoryName;

    @TableField("QUALITY_LEVEL")
    private String qualityLevel;

    @TableField("QUANTITY")
    private BigDecimal quantity;

    @TableField("STORE_ID")
    private Integer storeId;

    @TableField("HOUSE_ID")
    private Integer houseId;

    @TableField("OIL_TANK_ID")
    private Integer oilTankId;

    @TableField("POSITION_ID")
    private Integer positionId;

    @TableField("PLAN_DOCUMENT_CODE")
    private String planDocumentCode;

    @TableField("BUY_AMOUNT")
    private BigDecimal buyAmount;

    @TableField("POLICY_FOOD_IN_COST")
    private BigDecimal policyFoodInCost;

    @TableField("EXTRA_SUBSIDY_STANDARD")
    private String extraSubsidyStandard;

    @TableField("BUY_SUBSIDY_STANDARD")
    private String buySubsidyStandard;

    @TableField("INVOICE_CODE")
    private String invoiceCode;

    @TableField("WAREHOUSE_TYPE")
    private String warehouseType;

    @TableField("STORAGE_STATUS")
    private String storageStatus;

    @TableField("CUSTODIAN_NAME")
    private String custodianName;

    @TableField("GRAIN_NATURE")
    private String grainNature;

    @TableField("IS_MOULD")
    private String isMould;

    @TableField("MYCOTOXIN")
    private String mycotoxin;

    @TableField("HARMFUL_HEAVY_METAL")
    private String harmfulHeavyMetal;

    @TableField("PESTICIDES")
    private String pesticides;

    @TableField("INSECT_PEST")
    private String insectPest;

    @TableField("DRUG_NAME")
    private String drugName;

    @TableField("DRUG_QUANTITY")
    private String drugQuantity;

    @TableField("DRUG_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date drugTime;

    @TableField("SALE_TARGET")
    private String saleTarget;

    @TableField("DISTRIBUTION_CHANNEL")
    private String distributionChannel;

    @TableField("SALE_AMOUNT")
    private BigDecimal saleAmount;

    @TableField("SALE_TYPE")
    private String saleType;

    @TableField("SALE_SUBSIDY_STANDARD")
    private String saleSubsidyStandard;

    @TableField("GRANARY_ID")
    private Integer granaryId;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;

    @TableField("CONTACTS")
    private String contacts;

    @TableField("CERTIFICATES_TYPE")
    private String certificatesType;

    @TableField("CERTIFICATES_CODE")
    private String certificatesCode;

    @TableField("TEL")
    private String tel;

    @TableField("CAT_CODE")
    private String carCode;

    @TableField("ADDRESS")
    private String address;

    @TableField("NOTICE_CODE")
    private String noticeCode;

    @TableField("NOTICE_STATUS")
    private String noticeStatus;

    @TableField("CUSTOMER")
    private String customer;

    @TableField("NAME")
    private String name;
}
