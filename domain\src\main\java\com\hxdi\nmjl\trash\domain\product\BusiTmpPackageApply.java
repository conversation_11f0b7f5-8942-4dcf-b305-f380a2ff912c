package com.hxdi.nmjl.trash.domain.product;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 包装申请
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("BUSI_TMP_PACKAGE_APPLY")
public class BusiTmpPackageApply extends DataEntity<BusiTmpPackageApply> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("CODE")
    private String code;

    @TableField("ENTERPRISE_ID")
    private Integer enterpriseId;

    @TableField("ENTERPRISE_CODE")
    private String enterpriseCode;

    @TableField("ENTERPRISE_NAME")
    private String enterpriseName;

    @TableField("PRODUCT_NAME")
    private String productName;

    @TableField("NET_WEIGHT")
    private String netWeight;

    @TableField("PACKAGE_STYLE")
    private String packageStyle;

    @TableField("INGREDIENTS")
    private String ingredients;

    @TableField("FOOD_PRODUCTION_LICENSE")
    private String foodProductionLicense;

    @TableField("QUALITY_LEVEL")
    private String qualityLevel;

    @TableField("MANUFACTURE_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date manufactureTime;

    @TableField("SHELF_LIFE")
    private String shelfLife;

    @TableField("ORIGIN")
    private String origin;

    @TableField("MANUFACTURE_ADDRESS")
    private String manufactureAddress;

    @TableField("PRODUCT_STANDARD")
    private String productStandard;

    @TableField("MATERIAL_TECHNOLOGY")
    private String materialTechnology;

    @TableField("STORAGE_METHOD")
    private String storageMethod;

    @TableField("EAT_METHOD")
    private String eatMethod;

    @TableField("MANUFACTURE_PHONE")
    private String manufacturePhone;

    @TableField("APPROVE_STATUS")
    private String approveStatus;

    @TableField("APPROVE_PERSON")
    private String approvePerson;

    @TableField("APPROVE_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date approveTime;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;
}
