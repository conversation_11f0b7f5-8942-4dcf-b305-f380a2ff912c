package com.hxdi.nmjl.trash.domain.raise;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 供应商
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("BUSI_RAISE_SUPPLIER")
@Deprecated
public class BusiRaiseSupplier extends DataEntity<BusiRaiseSupplier> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("CODER")
    private String coder;

    @TableField("NAME")
    private String name;

    @TableField("TYPER")
    private String typer;

    @TableField("NICK_NAME")
    private String nickName;

    @TableField("COMPANY_TYPE")
    private String companyType;

    @TableField("PROVINCE_ID")
    private Integer provinceId;

    @TableField("PROVINCE")
    private String province;

    @TableField("CITY_ID")
    private Integer cityId;

    @TableField("CITY")
    private String city;

    @TableField("COUNTY_ID")
    private Integer countyId;

    @TableField("COUNTY")
    private String county;

    @TableField("ADDRESS")
    private String address;

    @TableField("LEGAL")
    private String legal;

    @TableField("PHONE")
    private String phone;

    @TableField("FAX")
    private String fax;

    @TableField("MAIL")
    private String mail;

    @TableField("WEBSITE")
    private String website;

    @TableField("AREA_CODE")
    private String areaCode;

    @TableField("REGISTER_TYPE")
    private String registerType;

    @TableField("ECONOMIC_TYPE")
    private String economicType;

    @TableField("BUSINESS_NO")
    private String businessNo;

    @TableField("STRUCTURE_CODE")
    private String structureCode;

    @TableField("CREDIT_CODE")
    private String creditCode;

    @TableField("LONGITUDE")
    private BigDecimal longitude;

    @TableField("LATITUDE")
    private BigDecimal latitude;

    @TableField("BANK")
    private String bank;

    @TableField("ACCOUNT")
    private String account;

    @TableField("BANK_CREDIT_LEVEL")
    private String bankCreditLevel;

    @TableField("FIXED_ASSETS")
    private String fixedAssets;

    @TableField("ASSETS")
    private String assets;

    @TableField("REGISTERED_CAPITAL")
    private BigDecimal registeredCapital;

    @TableField("EMPLOYMENTS")
    private Integer employments;

    @TableField("PRODUCTION_LINE")
    private String productionLine;

    @TableField("PRODUCTION_EQUIPMENT")
    private String productionEquipment;

    @TableField("EQUIPMENT_NAME")
    private String equipmentName;

    @TableField("EQUIPMENT_ORIGIN")
    private String equipmentOrigin;

    @TableField("PROCESS_CAPACITY")
    private String processCapacity;

    @TableField("FOOD_PRODUCTION_LICENSE")
    private String foodProductionLicense;

    @TableField("MAIN_BRAND")
    private String mainBrand;

    @TableField("QUALIFICATION")
    private String qualification;

    @TableField("GRADE")
    private String grade;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;

    @TableField("CAPACITY")
    private String capacity;

    @TableField("STATUS")
    private String status;
}
