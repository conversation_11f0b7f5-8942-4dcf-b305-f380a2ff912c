package com.hxdi.nmjl.trash.domain.supply;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 商品
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("BUSI_SUPPLY_GOODS")
public class BusiSupplyGoods extends DataEntity<BusiSupplyGoods> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("STATION_ID")
    private Integer stationId;

    @TableField("STATION_NAME")
    private String stationName;

    @TableField("CATEGORY")
    private String category;

    @TableField("VARIETY")
    private String variety;

    @TableField("BRAND")
    private String brand;

    @TableField("NAME")
    private String name;

    @TableField("SPEC")
    private String spec;

    @TableField("UNIT")
    private String unit;

    @TableField("AMOUNT")
    private BigDecimal amount;

    @TableField("QUALITY_LEVEL")
    private String qualityLevel;

    @TableField("PROVINCE_ID")
    private Integer provinceId;

    @TableField("PROVINCE")
    private String province;

    @TableField("CITY_ID")
    private Integer cityId;

    @TableField("CITY")
    private String city;

    @TableField("COUNTY_ID")
    private Integer countyId;

    @TableField("COUNTY")
    private String county;

    @TableField("STORE_ID")
    private Integer storeId;

    @TableField("SUPPLIER_ID")
    private Integer supplierId;

    @TableField("DESC_TITLE")
    private String descTitle;

    @TableField("DESC_CONTENT")
    private String descContent;

    @TableField("AFTER_SALES_DESC")
    private String afterSalesDesc;

    @TableField("RECEIVE_TYPE")
    private String receiveType;

    @TableField("SELF_TAKE_ADDRESS")
    private String selfTakeAddress;

    @TableField("ON_OFF_STATUS")
    private String onOffStatus;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;
}
