package com.hxdi.nmjl.trash.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.nmjl.entity.DataEntity;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 库点
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Getter
@Setter
@TableName("INFO_STORE")
public class InfoStore extends DataEntity<InfoStore> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("NAME")
    private String name;

    @TableField("NICK_NAME")
    private String nickName;

    @TableField("CODER")
    private String coder;

    @TableField("TYPER")
    private String typer;

    @TableField("UNIT_TYPER")
    private String unitTyper;

    @TableField("UNIT_ID")
    private Integer unitId;

    @TableField("UNIT_NAME")
    private String unitName;

    @TableField("BUILD_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date buildDate;

    @TableField("PROVINCE_ID")
    private Integer provinceId;

    @TableField("PROVINCE")
    private String province;

    @TableField("CITY_ID")
    private Integer cityId;

    @TableField("CITY")
    private String city;

    @TableField("COUNTY_ID")
    private Integer countyId;

    @TableField("COUNTY")
    private String county;

    @TableField("ADDR")
    private String addr;

    @TableField("LONGITUDE")
    private BigDecimal longitude;

    @TableField("LATITUDE")
    private BigDecimal latitude;

    @TableField("CAPACITY")
    private BigDecimal capacity;

    @TableField("STORE_AREA")
    private BigDecimal storeArea;

    @TableField("CAPACITY_COUNT")
    private BigDecimal capacityCount;

    @TableField("BELONG_AREA")
    private String belongArea;

    @TableField("RELATIVE_DISTANCE")
    private String relativeDistance;

    @TableField("CREATE_ID")
    private String createId;

    @TableField("UPDATE_ID")
    private String updateId;

    @TableField("TENANT_ID")
    private String tenantId;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;
}
